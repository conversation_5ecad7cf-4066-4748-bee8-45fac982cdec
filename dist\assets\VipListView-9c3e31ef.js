import{d as Te,r as p,a as P,by as Ce,c as k,b as e,w as a,h as t,A,cf as Ge,E as R,e as De,C as xe,o as _,T as Ee,I as D,f as i,G as K,H as Q,aC as Se,U as s,i as r,F as x,cg as Ie,k as Ae,l as Ne,m as Ue,n as Ye,X as Me,p as Fe,q as Le,s as ze,ch as Pe,ae as Re,v as qe,y as Oe,x as Be,Y as He,ci as $e,cj as je,al as Xe,am as Ze,Z as Je,bb as Ke,bc as Qe,ck as We,_ as ea}from"./index-8fc71dda.js";/* empty css                   *//* empty css                       *//* empty css                 *//* empty css                  *//* empty css                   *//* empty css                      *//* empty css                 *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                     *//* empty css                       */const N=E=>(Ke("data-v-6e960315"),E=E(),Qe(),E),aa={class:"header-box"},la={class:"mode-box"},ta=["onClick"],oa={class:"user-info"},na={class:"user-details"},sa={class:"mobile"},ra={class:"package-info"},ia={class:"package-name"},da={class:"duration"},ua={class:"amount-info"},ca={class:"pay-amount"},ma={key:0,class:"inviter-info"},pa={class:"inviter-name"},_a={key:1,class:"no-inviter"},ga={class:"commission-info"},fa={key:0},va={key:0,class:"order-detail"},ba={class:"dialog-footer"},ka={class:"package-option"},ha={class:"package-name"},ya={class:"package-price"},wa={class:"package-preview"},Va={class:"package-detail"},Ta=N(()=>i("strong",null,"套餐名称：",-1)),Ca=N(()=>i("strong",null,"套餐类型：",-1)),Ga=N(()=>i("strong",null,"套餐价格：",-1)),Da=N(()=>i("strong",null,"套餐时长：",-1)),xa={class:"dialog-footer"},Ea=Te({__name:"VipListView",setup(E){const U=p(!1),Y=p(0),M=p(""),S=p(null),V=p(!1),d=p(null),C=p(!1),F=p(!1),G=p(),b=p("userGuid"),I=p([]),h=p(null),m=P({merchantGuid:"",packageGuid:"",userGuid:"",nickname:""}),W={packageGuid:[{required:!0,message:"请选择套餐",trigger:"change"}],userGuid:[{required:!0,message:"请输入用户GUID",trigger:"blur",validator:(o,n,v)=>{b.value==="userGuid"&&!n?v(new Error("请输入用户GUID")):v()}}],nickname:[{required:!0,message:"请输入用户昵称",trigger:"blur",validator:(o,n,v)=>{b.value==="nickname"&&!n?v(new Error("请输入用户昵称")):v()}}]},ee=P([{name:"全部",value:""},{name:"待支付",value:100},{name:"已支付",value:200},{name:"取消支付",value:300},{name:"支付超时",value:400},{name:"已退款",value:500}]),u=P({merchantGuid:"",orderStatus:"",packageType:"",orderNo:"",userMobile:"",startTime:"",endTime:"",page:1,limit:20}),L=p([]),q=o=>{switch(o){case 100:return"warning";case 200:return"success";case 300:return"info";case 400:return"danger";case 500:return"primary";default:return"info"}},ae=o=>{M.value=o.toString(),u.orderStatus=o,u.page=1,y()},le=o=>{o?(u.startTime=o[0],u.endTime=o[1]):(u.startTime="",u.endTime="")},y=async()=>{try{U.value=!0;const o=await Ge(u);o.data&&(Y.value=o.data.total||0,L.value=o.data.data||[])}catch(o){console.error("获取订单列表失败:",o),R.error("获取订单列表失败"),L.value=[],Y.value=0}finally{U.value=!1}},te=()=>{u.page=1,y()},oe=()=>{Object.assign(u,{orderStatus:"",packageType:"",orderNo:"",userMobile:"",startTime:"",endTime:"",page:1,limit:20}),M.value="",S.value=null,y()},ne=o=>{u.page=o,y()},se=o=>{u.limit=o,u.page=1,y()},re=o=>{d.value=o,V.value=!0},ie=()=>{V.value=!1,d.value=null},de=o=>{switch(o){case 1:return"月卡";case 2:return"季卡";case 3:return"年卡";case 4:return"自定义";default:return"未知"}},ue=async()=>{try{const o=await We({});o.data&&(I.value=o.data.data||o.data||[])}catch(o){console.error("获取套餐列表失败:",o),R.error("获取套餐列表失败"),I.value=[]}},ce=()=>{C.value=!0,ue(),O()},O=()=>{var o;Object.assign(m,{merchantGuid:"",packageGuid:"",userGuid:"",nickname:""}),b.value="userGuid",h.value=null,(o=G.value)==null||o.clearValidate()},me=o=>{h.value=I.value.find(n=>n.guid===o)||null},pe=o=>{var n;o==="userGuid"?m.nickname="":m.userGuid="",(n=G.value)==null||n.clearValidate()},z=()=>{C.value=!1,O()},_e=async()=>{if(G.value)try{await G.value.validate(),F.value=!0;const o={packageGuid:m.packageGuid};b.value==="userGuid"?o.userGuid=m.userGuid:o.nickname=m.nickname;const{data:n}=await Ie(o);n.data?(z(),y()):R.warning(n.message)}catch(o){console.error("添加会员失败:",o)}finally{F.value=!1}};return Ce(()=>{y()}),(o,n)=>{const v=Ae,f=Ne,T=Ue,B=Ye,ge=Me,w=Fe,H=Le,g=ze,$=Pe,j=Re,fe=qe,ve=Oe,be=Be,ke=He,he=De,c=$e,ye=je,X=xe,Z=Xe,we=Ze,Ve=Je;return _(),k("div",null,[e(he,{class:"wrapper"},{default:a(()=>[Ee((_(),D(ve,null,{default:a(()=>[i("div",aa,[e(H,{inline:!0,model:t(u),class:"demo-form-inline"},{default:a(()=>[e(f,{label:"订单编号"},{default:a(()=>[e(v,{modelValue:t(u).orderNo,"onUpdate:modelValue":n[0]||(n[0]=l=>t(u).orderNo=l),placeholder:"请输入订单编号",clearable:""},null,8,["modelValue"])]),_:1}),e(f,{label:"用户手机号"},{default:a(()=>[e(v,{modelValue:t(u).userMobile,"onUpdate:modelValue":n[1]||(n[1]=l=>t(u).userMobile=l),placeholder:"请输入用户手机号",clearable:""},null,8,["modelValue"])]),_:1}),e(f,{label:"订单状态"},{default:a(()=>[i("div",la,[(_(!0),k(K,null,Q(t(ee),(l,J)=>(_(),k("div",{class:Se(["item",{active:t(M)===l.value}]),key:J,onClick:Sa=>ae(l.value)},s(l.name),11,ta))),128))])]),_:1}),e(f,{label:"套餐类型"},{default:a(()=>[e(B,{modelValue:t(u).packageType,"onUpdate:modelValue":n[2]||(n[2]=l=>t(u).packageType=l),placeholder:"请选择套餐类型",clearable:""},{default:a(()=>[e(T,{label:"全部",value:""}),e(T,{label:"月卡",value:1}),e(T,{label:"季卡",value:2}),e(T,{label:"年卡",value:3}),e(T,{label:"自定义",value:4})]),_:1},8,["modelValue"])]),_:1}),e(f,{label:"时间范围"},{default:a(()=>[e(ge,{modelValue:t(S),"onUpdate:modelValue":n[3]||(n[3]=l=>A(S)?S.value=l:null),type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",onChange:le},null,8,["modelValue"])]),_:1}),e(f,null,{default:a(()=>[e(w,{type:"primary",onClick:te},{default:a(()=>[r("搜索")]),_:1}),e(w,{onClick:oe},{default:a(()=>[r("重置")]),_:1}),e(w,{type:"success",onClick:ce},{default:a(()=>[r("新增会员")]),_:1})]),_:1})]),_:1},8,["model"])]),e(fe,{data:t(L),border:"",style:{width:"100%"}},{default:a(()=>[e(g,{prop:"sysId",label:"订单ID",width:"80"}),e(g,{label:"购买用户",width:"200"},{default:a(l=>[i("div",oa,[e($,{src:l.row.buyer.headImgurl,size:30},null,8,["src"]),i("div",na,[i("div",null,s(l.row.buyer.nickname),1),i("div",sa,s(l.row.buyer.mobile),1)])])]),_:1}),e(g,{prop:"orderNo",label:"订单编号",width:"180"}),e(g,{label:"套餐信息",width:"200"},{default:a(l=>[i("div",ra,[i("div",ia,s(l.row.package.packageName),1),i("div",da,s(l.row.durationDays)+"天",1)])]),_:1}),e(g,{label:"金额信息",width:"150"},{default:a(l=>[i("div",ua,[i("div",ca,"支付: ¥"+s(l.row.payAmountYuan),1)])]),_:1}),e(g,{prop:"orderStatusText",label:"订单状态",width:"100"},{default:a(l=>[e(j,{type:q(l.row.orderStatus),size:"small"},{default:a(()=>[r(s(l.row.orderStatusText),1)]),_:2},1032,["type"])]),_:1}),e(g,{label:"邀请人",width:"150"},{default:a(l=>[l.row.inviter?(_(),k("div",ma,[e($,{src:l.row.inviter.headImgurl,size:24},null,8,["src"]),i("span",pa,s(l.row.inviter.nickname),1)])):(_(),k("span",_a,"无"))]),_:1}),e(g,{label:"佣金信息",width:"120"},{default:a(l=>[i("div",ga,[i("div",null,"平台费: ¥"+s(l.row.platformFeeYuan),1),l.row.inviterCommissionYuan?(_(),k("div",fa," 邀请佣金: ¥"+s(l.row.inviterCommissionYuan),1)):x("",!0)])]),_:1}),e(g,{prop:"createTimeText",label:"创建时间",width:"160"}),e(g,{prop:"payTimeText",label:"支付时间",width:"160"}),e(g,{prop:"expireTimeText",label:"到期时间",width:"160"}),e(g,{label:"操作",width:"120",fixed:"right"},{default:a(l=>[e(w,{size:"small",type:"primary",onClick:J=>re(l.row)},{default:a(()=>[r(" 查看详情 ")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1})),[[Ve,t(U)]]),e(ke,null,{default:a(()=>[e(be,{background:"",layout:"total, sizes, prev, pager, next, jumper",total:t(Y),"current-page":t(u).page,"page-size":t(u).limit,"page-sizes":[10,20,50,100],onCurrentChange:ne,onSizeChange:se},null,8,["total","current-page","page-size"])]),_:1})]),_:1}),e(X,{modelValue:t(V),"onUpdate:modelValue":n[5]||(n[5]=l=>A(V)?V.value=l:null),title:"订单详情",width:"800px","before-close":ie},{footer:a(()=>[i("span",ba,[e(w,{onClick:n[4]||(n[4]=l=>V.value=!1)},{default:a(()=>[r("关闭")]),_:1})])]),default:a(()=>[t(d)?(_(),k("div",va,[e(ye,{column:2,border:""},{default:a(()=>[e(c,{label:"订单ID"},{default:a(()=>[r(s(t(d).sysId),1)]),_:1}),e(c,{label:"订单编号"},{default:a(()=>[r(s(t(d).orderNo),1)]),_:1}),e(c,{label:"交易流水号"},{default:a(()=>[r(s(t(d).transactionNo||"无"),1)]),_:1}),e(c,{label:"订单状态"},{default:a(()=>[e(j,{type:q(t(d).orderStatus)},{default:a(()=>[r(s(t(d).orderStatusText),1)]),_:1},8,["type"])]),_:1}),e(c,{label:"购买用户"},{default:a(()=>[r(s(t(d).buyerNickname),1)]),_:1}),e(c,{label:"用户手机"},{default:a(()=>[r(s(t(d).buyerMobile),1)]),_:1}),e(c,{label:"套餐名称"},{default:a(()=>[r(s(t(d).packageName),1)]),_:1}),e(c,{label:"套餐类型"},{default:a(()=>[r(s(t(d).packageTypeText),1)]),_:1}),e(c,{label:"套餐时长"},{default:a(()=>[r(s(t(d).durationDays)+"天",1)]),_:1}),e(c,{label:"原价"},{default:a(()=>[r("¥"+s(t(d).originalAmountYuan),1)]),_:1}),e(c,{label:"支付金额"},{default:a(()=>[r("¥"+s(t(d).payAmountYuan),1)]),_:1}),e(c,{label:"平台费用"},{default:a(()=>[r("¥"+s(t(d).platformFeeYuan),1)]),_:1}),e(c,{label:"邀请人"},{default:a(()=>[r(s(t(d).inviterNickname||"无"),1)]),_:1}),e(c,{label:"邀请佣金"},{default:a(()=>[r("¥"+s(t(d).inviterCommissionYuan||"0"),1)]),_:1}),e(c,{label:"创建时间"},{default:a(()=>[r(s(t(d).createTimeText),1)]),_:1}),e(c,{label:"支付时间"},{default:a(()=>[r(s(t(d).payTimeText||"未支付"),1)]),_:1}),e(c,{label:"到期时间"},{default:a(()=>[r(s(t(d).expireTimeText),1)]),_:1})]),_:1})])):x("",!0)]),_:1},8,["modelValue"]),e(X,{modelValue:t(C),"onUpdate:modelValue":n[10]||(n[10]=l=>A(C)?C.value=l:null),title:"新增会员",width:"600px","before-close":z},{footer:a(()=>[i("span",xa,[e(w,{onClick:z},{default:a(()=>[r("取消")]),_:1}),e(w,{type:"primary",onClick:_e,loading:t(F)},{default:a(()=>[r("确认添加")]),_:1},8,["loading"])])]),default:a(()=>[e(H,{model:t(m),rules:W,ref_key:"addFormRef",ref:G,"label-width":"120px"},{default:a(()=>[e(f,{label:"套餐选择",prop:"packageGuid",required:""},{default:a(()=>[e(B,{modelValue:t(m).packageGuid,"onUpdate:modelValue":n[6]||(n[6]=l=>t(m).packageGuid=l),placeholder:"请选择套餐",style:{width:"100%"},onChange:me},{default:a(()=>[(_(!0),k(K,null,Q(t(I),l=>(_(),D(T,{key:l.guid,label:`${l.packageName} - ¥${l.salePrice/100}`,value:l.guid},{default:a(()=>[i("div",ka,[i("span",ha,s(l.packageName),1),i("span",ya,"¥"+s(l.salePrice/100),1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(f,{label:"用户选择",required:""},{default:a(()=>[e(we,{modelValue:t(b),"onUpdate:modelValue":n[7]||(n[7]=l=>A(b)?b.value=l:null),onChange:pe},{default:a(()=>[e(Z,{label:"userGuid"},{default:a(()=>[r("用户GUID")]),_:1}),e(Z,{label:"nickname"},{default:a(()=>[r("用户昵称")]),_:1})]),_:1},8,["modelValue"])]),_:1}),t(b)==="userGuid"?(_(),D(f,{key:0,label:"用户GUID",prop:"userGuid"},{default:a(()=>[e(v,{modelValue:t(m).userGuid,"onUpdate:modelValue":n[8]||(n[8]=l=>t(m).userGuid=l),placeholder:"请输入用户GUID",clearable:""},null,8,["modelValue"])]),_:1})):x("",!0),t(b)==="nickname"?(_(),D(f,{key:1,label:"用户昵称",prop:"nickname"},{default:a(()=>[e(v,{modelValue:t(m).nickname,"onUpdate:modelValue":n[9]||(n[9]=l=>t(m).nickname=l),placeholder:"请输入用户昵称",clearable:""},null,8,["modelValue"])]),_:1})):x("",!0),t(h)?(_(),D(f,{key:2,label:"套餐信息"},{default:a(()=>[i("div",wa,[i("div",Va,[i("div",null,[Ta,r(s(t(h).packageName),1)]),i("div",null,[Ca,r(s(de(t(h).packageType)),1)]),i("div",null,[Ga,r("¥"+s(t(h).salePrice/100),1)]),i("div",null,[Da,r(s(t(h).durationDays)+"天",1)])])])]),_:1})):x("",!0)]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}});const Ba=ea(Ea,[["__scopeId","data-v-6e960315"]]);export{Ba as default};
