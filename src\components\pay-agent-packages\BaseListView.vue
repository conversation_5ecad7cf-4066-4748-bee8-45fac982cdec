<script setup lang="ts">
import { getPayAgentPackageListApi } from '@/api';
import { ElMessage } from 'element-plus';

// 分页参数
const total = ref(0);
const loading = ref(false);

// 搜索筛选参数
const searchParams = reactive({
  merchantGuid: '', // 商户uuid，不需要填写
  pageSize: 100,
  page: 1,
});

// 数据类型定义
interface PackageItem {
  guid: string;
  status: number;
  sysId: number;
  merchantGuid: string;
  packageName: string;
  packageDesc: string;
  packageType: number;
  durationDays: number;
  originalPrice: number;
  salePrice: number;
  packageIcon: string;
  packageFeatures: {
    [key: string]: string;
  };
  rewardPoints: number;
  sortOrder: number;
  isHot: number;
  isRecommended: number;
  createTime: number;
  updateTime: number;
  deletedAt: string | null;
  modifyTime: string;
  packageTypeText: string;
  statusText: string;
  originalPriceYuan: string;
  salePriceYuan: string;
  rewardPointsText: string;
  createTimeText: string;
}

// 列表数据
const packageList = ref<PackageItem[]>([]);

// 获取套餐列表
const getList = async () => {
  loading.value = true;
  try {
    const res = await getPayAgentPackageListApi(searchParams);
    if (res.data && Array.isArray(res.data)) {
      // 直接数组格式
      packageList.value = res.data;
      total.value = res.data.length;
    } else if (res.data && res.data.data) {
      // 分页格式
      packageList.value = res.data.data;
      total.value = res.data.total;
    }
  } catch (error) {
    ElMessage.error('获取套餐列表失败');
  } finally {
    loading.value = false;
  }
};

// 搜索功能
const handleSearch = () => {
  searchParams.page = 1;
  getList();
};

// 重置搜索
const handleReset = () => {
  searchParams.page = 1;
  getList();
};

// 分页切换
const handlePageChange = (page: number) => {
  searchParams.page = page;
  getList();
};

// 每页条数切换
const handleSizeChange = (size: number) => {
  searchParams.pageSize = size;
  searchParams.page = 1;
  getList();
};

// 获取套餐类型标签样式
const getPackageTypeTag = (type: number) => {
  const tagMap = {
    1: 'success', // 月卡
    2: 'warning', // 季卡
    3: 'info',    // 年卡
  };
  return tagMap[type] || '';
};

// 获取状态标签样式
const getStatusTag = (status: number) => {
  return status === 1 ? 'success' : 'danger';
};

// 初始化加载数据
getList();
</script>

<template>
  <div>
    <el-container class="wrapper">
      <el-main>
        <!-- 搜索筛选区域 -->
        <div class="search-box">
          <el-form :inline="true" class="search-form">
            <el-form-item>
              <el-button type="primary" @click="handleSearch">搜索</el-button>
              <el-button @click="handleReset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 表格区域 -->
        <el-table :data="packageList" border style="width: 100%" v-loading="loading">
          <el-table-column prop="sysId" label="ID" width="80" />
          <el-table-column prop="packageName" label="套餐名称" min-width="150" />
          <el-table-column prop="packageDesc" label="套餐描述" min-width="200" show-overflow-tooltip />
          <el-table-column prop="packageTypeText" label="套餐类型" width="100">
            <template #default="scope">
              <el-tag :type="getPackageTypeTag(scope.row.packageType)">
                {{ scope.row.packageTypeText }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="durationDays" label="有效期(天)" width="100" />
          <el-table-column prop="originalPriceYuan" label="原价(元)" width="100" />
          <el-table-column prop="salePriceYuan" label="售价(元)" width="100" />
          <el-table-column prop="rewardPointsText" label="奖励积分" width="100" />
          <!-- <el-table-column prop="sortOrder" label="排序" width="80" /> -->
          <el-table-column prop="statusText" label="状态" width="80">
            <template #default="scope">
              <el-tag :type="getStatusTag(scope.row.status)">
                {{ scope.row.statusText }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createTimeText" label="创建时间" width="180" />
        </el-table>

        <!-- 分页组件 -->
        <div class="pagination-container">
          <el-pagination v-model:current-page="searchParams.page" v-model:page-size="searchParams.pageSize"
            :page-sizes="[10, 20, 50, 100]" :total="total" layout="total, sizes, prev, pager, next, jumper"
            :prev-text="'上一页'" :next-text="'下一页'" @size-change="handleSizeChange" @current-change="handlePageChange" />
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<style scoped lang="scss">
.search-box {
  background: #f5f7fa;
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.search-form {
  margin: 0;
}

.search-form .el-form-item {
  margin-bottom: 0;
}

.pagination-container {
  display: flex;
  justify-content: flex-start;
  margin-top: 20px;
  padding: 20px 0;
}
</style>