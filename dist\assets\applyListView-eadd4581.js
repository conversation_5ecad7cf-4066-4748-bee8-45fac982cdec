import{d as H,r as c,a as v,c as f,b as e,w as t,h as l,A as O,bl as Y,e as Z,C as j,o as i,T as J,I as x,f as b,G as S,H as L,aC as K,U as Q,i as y,bm as W,E as X,l as ee,p as te,q as ae,s as le,v as oe,y as ne,x as se,Y as pe,m as ie,n as re,k as de,Z as ue,_ as ce}from"./index-8fc71dda.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                 *//* empty css               *//* empty css                  *//* empty css                      *//* empty css                        *//* empty css                     */const _e={class:"hearder-box"},me={class:"mode-box"},fe=["onClick"],ye={class:"dialog-footer"},ge=H({__name:"applyListView",setup(ve){const w=c(0),g=c(!1);let h=c("");const C=v([{id:3,type:"",name:"全部"},{id:0,type:"100",name:"待审核"},{id:1,type:"200",name:"审核通过"},{id:2,type:"300",name:"审核拒绝"}]),r=v({merchantGuid:"",applyStatus:"",pageSize:10,page:1});let V=c([]);const B=o=>{h.value=o,r.applyStatus=o,d()},d=async()=>{g.value=!0;let o=await Y(r);g.value=!1,w.value=o.data.total,V.value=o.data.data},R=async o=>{r.page=o,d()},p=v({guid:"",applyStatus:"200",refuseReason:""}),u=c(!1),F=o=>{p.guid=o.guid,u.value=!0},I=async()=>{try{await W(p),X({message:"审核通过",type:"success"}),d()}catch{}},T=()=>{d()};return d(),(o,n)=>{const _=ee,m=te,E=ae,s=le,A=oe,U=ne,z=se,D=pe,M=Z,N=ie,$=re,q=de,G=j,P=ue;return i(),f("div",null,[e(M,{class:"wrapper"},{default:t(()=>[J((i(),x(U,null,{default:t(()=>[b("div",_e,[e(E,{inline:!0,model:l(r),class:"demo-form-inline"},{default:t(()=>[e(_,{label:"审核状态"},{default:t(()=>[b("div",me,[(i(!0),f(S,null,L(l(C),(a,k)=>(i(),f("div",{class:K(["item",{active:l(h)===a.type}]),key:k,onClick:be=>B(a.type)},Q(a.name),11,fe))),128))])]),_:1}),e(_,null,{default:t(()=>[e(m,{type:"primary",onClick:T},{default:t(()=>[y("搜索")]),_:1})]),_:1})]),_:1},8,["model"])]),e(A,{data:l(V),border:"",style:{width:"100%"}},{default:t(()=>[e(s,{prop:"user.sysId",label:"用户Id",width:"80"}),e(s,{prop:"user.nickname",label:"用户昵称",width:"180"}),e(s,{prop:"user.mobile",label:"手机号",width:"120"}),e(s,{prop:"applyRemark","show-overflow-tooltip":!0,label:"申请备注",width:"120"}),e(s,{prop:"statusText",label:"审核状态"}),e(s,{prop:"refuseReason",label:"拒绝理由"}),e(s,{prop:"createTime",label:"申请时间"}),e(s,{label:"操作"},{default:t(a=>[e(m,{size:"small",type:"primary",onClick:k=>F(a.row)},{default:t(()=>[y("审批")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1})),[[P,l(g)]]),e(D,null,{default:t(()=>[e(z,{background:"",layout:"prev,pager, next",total:l(w),"current-page":l(r).page,onCurrentChange:R},null,8,["total","current-page"])]),_:1})]),_:1}),e(G,{modelValue:l(u),"onUpdate:modelValue":n[3]||(n[3]=a=>O(u)?u.value=a:null),title:"Shipping address",width:"500"},{footer:t(()=>[b("div",ye,[e(m,{onClick:n[2]||(n[2]=a=>u.value=!1)},{default:t(()=>[y("取 消")]),_:1}),e(m,{type:"primary",onClick:I},{default:t(()=>[y(" 确 认 ")]),_:1})])]),default:t(()=>[e(E,{model:l(p)},{default:t(()=>[e(_,{label:"审批状态","label-width":"80px"},{default:t(()=>[e($,{modelValue:l(p).applyStatus,"onUpdate:modelValue":n[0]||(n[0]=a=>l(p).applyStatus=a)},{default:t(()=>[(i(!0),f(S,null,L(l(C),a=>(i(),x(N,{label:a.name,value:a.type},null,8,["label","value"]))),256))]),_:1},8,["modelValue"])]),_:1}),e(_,{label:"拒绝理由","label-width":"80px"},{default:t(()=>[e(q,{modelValue:l(p).refuseReason,"onUpdate:modelValue":n[1]||(n[1]=a=>l(p).refuseReason=a),autocomplete:"off",placeholder:"非拒绝则不填"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}});const Re=ce(ge,[["__scopeId","data-v-2241c4bc"]]);export{Re as default};
