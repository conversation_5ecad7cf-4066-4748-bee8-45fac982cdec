import{d as M,r as u,a as p,c as R,b as e,w as t,h as a,az as U,e as Y,C as Z,u as j,o as E,f as O,i as d,T as X,I as H,aX as J,E as m,aY as Q,ax as W,aZ as ee,p as te,l as oe,q as ae,s as ne,af as le,v as se,y as ie,x as re,Y as de,k as ce,Z as ue}from"./index-8fc71dda.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                 *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                   *//* empty css                     */const pe={class:"header-box"},Te=M({__name:"KnowledgeListView",setup(me){const y=j(),v=u(!1),_=p({merchantGuid:"",pageSize:10,page:1}),k=u([]),h=u(0),c=async()=>{let o=await U(_);k.value=o.data.data,h.value=o.data.total};c();let r=p({merchantGuid:"",knowledgeTitle:""});const n=p({dialogtype:"ADD",isShow:!1}),T=p({knowledgeTitle:[{required:!0,message:"请输入知识库名称",trigger:"change"}]}),L=()=>{n.isShow=!0,n.dialogtype="ADD"},b=u(),x=async o=>{o.validate(async i=>{if(i)try{n.dialogtype==="ADD"?(await J(r),m.success("新增成功")):n.dialogtype==="EDIT"&&(await Q(r),m.success("修改成功")),W(()=>{o.resetFields(),n.isShow=!1}),c()}catch(l){throw m.error(l),new Error(l)}})},D=o=>{r=Object.assign(r,o),n.dialogtype="EDIT",n.isShow=!0},V=o=>{_.page=o,c()},S=o=>{y.push({name:"knowledgedetail",query:{guid:o.guid}})},A=o=>{y.push({name:"KnowledgeTestView",query:{guid:o.guid}})},K=async o=>{await ee({guid:o.guid}),m.success("删除成功"),c()};return(o,i)=>{const l=te,g=oe,C=ae,f=ne,z=le,F=se,I=ie,q=re,B=de,$=Y,N=ce,P=Z,G=ue;return E(),R("div",null,[e($,{class:"wrapper"},{default:t(()=>[e(I,null,{default:t(()=>[O("div",pe,[e(C,{inline:!0,class:"demo-form-inline"},{default:t(()=>[e(g,null,{default:t(()=>[e(l,{type:"primary",onClick:L},{default:t(()=>[d("新增配置")]),_:1})]),_:1})]),_:1})]),X((E(),H(F,{data:a(k),border:"",style:{width:"100%"}},{default:t(()=>[e(f,{prop:"sysId",label:"id",width:"80"}),e(f,{prop:"knowledgeTitle",label:"知识库名称"}),e(f,{label:"操作",width:"420"},{default:t(s=>[e(l,{size:"small",type:"primary",onClick:w=>S(s.row)},{default:t(()=>[d("知识库文档")]),_:2},1032,["onClick"]),e(l,{size:"small",type:"primary",onClick:w=>A(s.row)},{default:t(()=>[d("知识库召回测试")]),_:2},1032,["onClick"]),e(l,{size:"small",type:"primary",onClick:w=>D(s.row)},{default:t(()=>[d("编辑")]),_:2},1032,["onClick"]),e(z,{"confirm-button-text":"确认","cancel-button-text":"取消","icon-color":"red",title:"是否删除该知识库?",onConfirm:w=>K(s.row)},{reference:t(()=>[e(l,{size:"small",type:"danger"},{default:t(()=>[d("删除")]),_:1})]),_:2},1032,["onConfirm"])]),_:1})]),_:1},8,["data"])),[[G,a(v)]])]),_:1}),e(B,null,{default:t(()=>[e(q,{background:"",layout:"prev,pager, next",total:a(h),"current-page":a(_).page,onCurrentChange:V},null,8,["total","current-page"])]),_:1})]),_:1}),e(P,{modelValue:a(n).isShow,"onUpdate:modelValue":i[2]||(i[2]=s=>a(n).isShow=s),title:"创建/修改 知识库",width:"600",draggable:""},{default:t(()=>[e(C,{ref_key:"addForm",ref:b,model:a(r),class:"demo-form-inline",rules:a(T),"label-width":"100px"},{default:t(()=>[e(g,{label:"知识库名称",prop:"knowledgeTitle"},{default:t(()=>[e(N,{modelValue:a(r).knowledgeTitle,"onUpdate:modelValue":i[0]||(i[0]=s=>a(r).knowledgeTitle=s),placeholder:"请输入知识库名称"},null,8,["modelValue"])]),_:1}),e(g,null,{default:t(()=>[e(l,{type:"primary",onClick:i[1]||(i[1]=s=>x(a(b)))},{default:t(()=>[d("提交")]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])])}}});export{Te as default};
