import { createApp } from 'vue';
import { createPinia } from 'pinia';

import App from './App.vue';
import router from '@/router/inspect';
import { Shop, UserFilled, Tools, Operation, Share, WalletFilled, Medal, Guide } from '@element-plus/icons-vue'; //看自己需求取
import 'normalize.css';

// Element Plus 中文语言包
import ElementPlus from 'element-plus';
import zhCn from 'element-plus/dist/locale/zh-cn.mjs';

import VMdEditor from '@kangc/v-md-editor';
import '@kangc/v-md-editor/lib/style/base-editor.css';
import githubTheme from '@kangc/v-md-editor/lib/theme/github.js';
import '@kangc/v-md-editor/lib/theme/style/github.css';

// highlightjs
import hljs from 'highlight.js';

VMdEditor.use(githubTheme, {
  Hljs: hljs,
});

const app = createApp(App);

app.component('Shop', Shop);
app.component('UserFilled', UserFilled);
app.component('Tools', Tools);
app.component('Share', Share);
app.component('Operation', Operation);
app.component('WalletFilled', WalletFilled);
app.component('Medal', Medal);
app.component('Guide', Guide);

// 使用 Element Plus 并配置中文语言
app.use(ElementPlus, {
  locale: zhCn,
});

app.use(createPinia());
app.use(router);
app.use(VMdEditor);
app.mount('#app');
