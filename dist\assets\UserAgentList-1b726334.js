import{d as oe,r as _,a as M,c as U,b as e,w as t,h as s,A as se,c4 as ue,E as w,e as de,C as ne,c8 as re,o as v,f as c,G as ie,H as pe,I as z,i as n,T as ce,U as g,F as I,c9 as ge,ca as me,m as _e,n as fe,l as ye,k as be,p as we,q as ve,s as he,ae as Se,v as Ve,x as ke,y as xe,al as Ae,am as Ce,Z as Ee,bb as Te,bc as Ue,_ as ze}from"./index-8fc71dda.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                       *//* empty css                 *//* empty css                      *//* empty css                 *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                     */const N=V=>(Te("data-v-56242a7b"),V=V(),Ue(),V),Ne={class:"search-box"},Re={class:"pagination-container"},Ge={key:0},Pe=N(()=>c("strong",null,"名称：",-1)),Le=N(()=>c("strong",null,"创作者：",-1)),Me=N(()=>c("strong",null,"当前状态：",-1)),Ie={class:"dialog-footer"},De=oe({__name:"UserAgentList",setup(V){const k=_(0),x=_(!1),u=M({merchantGuid:"",categoryGuid:"",agentType:"",auditStatus:"",status:"",isPaid:"",agentName:"",creatorNickname:"",pageSize:10,page:1});let A=_([]),C=_([]);const f=_(!1),E=_(),y=_(null),r=M({guid:"",auditStatus:2,auditRemark:""}),D={auditStatus:[{required:!0,message:"请选择审核状态",trigger:"change"}],auditRemark:[{validator:(l,o,d)=>{r.auditStatus===3&&!o?d(new Error("审核拒绝时必须填写拒绝原因")):d()},trigger:"blur"}]},m=async()=>{x.value=!0;try{let l=await re(u);l.data&&Array.isArray(l.data)?(A.value=l.data,k.value=l.data.length):l.data&&l.data.data&&(A.value=l.data.data,k.value=l.data.total)}catch{w.error("获取智能体列表失败")}finally{x.value=!1}},F=async()=>{try{let l=await ue({merchantGuid:u.merchantGuid});l.data&&Array.isArray(l.data)?C.value=l.data:l.data&&l.data.data&&(C.value=l.data.data),m()}catch{w.error("获取分类列表失败")}},B=()=>{u.page=1,m()},$=()=>{u.categoryGuid="",u.agentType="",u.auditStatus="",u.status="",u.isPaid="",u.agentName="",u.creatorNickname="",u.page=1,m()},q=l=>{u.page=l,m()},j=l=>{u.pageSize=l,u.page=1,m()},H=l=>{y.value=l,r.guid=l.guid||"",r.auditStatus=2,r.auditRemark="",f.value=!0},O=async()=>{E.value&&await E.value.validate(async l=>{if(l)try{await ge({guid:r.guid,auditStatus:r.auditStatus,auditRemark:r.auditRemark}),w.success("审核操作成功"),f.value=!1,m()}catch{w.error("审核操作失败")}})},Z=async l=>{try{const o=l.status===1?2:1;await me({guid:l.guid,status:o}),w.success(`${o===1?"启用":"禁用"}成功`),m()}catch{w.error("状态修改失败")}},J=l=>({1:"内部",2:"dify",3:"coze",4:"阿里云百炼"})[l]||"未知",K=l=>({1:"",2:"success",3:"warning",4:"info"})[l]||"",R=l=>({1:"待审核",2:"审核通过",3:"审核拒绝"})[l]||"未知",G=l=>({1:"warning",2:"success",3:"danger"})[l]||"";return F(),(l,o)=>{const d=_e,h=fe,i=ye,T=be,b=we,P=ve,p=he,S=Se,Q=Ve,W=ke,X=xe,Y=de,L=Ae,ee=Ce,ae=ne,te=Ee;return v(),U("div",null,[e(Y,{class:"wrapper"},{default:t(()=>[e(X,null,{default:t(()=>[c("div",Ne,[e(P,{inline:!0,class:"search-form"},{default:t(()=>[e(i,{label:"分类"},{default:t(()=>[e(h,{modelValue:s(u).categoryGuid,"onUpdate:modelValue":o[0]||(o[0]=a=>s(u).categoryGuid=a),placeholder:"请选择分类",clearable:"",style:{width:"200px"}},{default:t(()=>[(v(!0),U(ie,null,pe(s(C),a=>(v(),z(d,{label:a.categoryName,value:a.guid,key:a.guid},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(i,{label:"智能体类型"},{default:t(()=>[e(h,{modelValue:s(u).agentType,"onUpdate:modelValue":o[1]||(o[1]=a=>s(u).agentType=a),placeholder:"请选择类型",clearable:"",style:{width:"150px"}},{default:t(()=>[e(d,{label:"内部",value:1}),e(d,{label:"dify",value:2}),e(d,{label:"coze",value:3}),e(d,{label:"阿里云百炼",value:4})]),_:1},8,["modelValue"])]),_:1}),e(i,{label:"审核状态"},{default:t(()=>[e(h,{modelValue:s(u).auditStatus,"onUpdate:modelValue":o[2]||(o[2]=a=>s(u).auditStatus=a),placeholder:"请选择审核状态",clearable:"",style:{width:"150px"}},{default:t(()=>[e(d,{label:"待审核",value:1}),e(d,{label:"审核通过",value:2}),e(d,{label:"审核拒绝",value:3})]),_:1},8,["modelValue"])]),_:1}),e(i,{label:"状态"},{default:t(()=>[e(h,{modelValue:s(u).status,"onUpdate:modelValue":o[3]||(o[3]=a=>s(u).status=a),placeholder:"请选择状态",clearable:"",style:{width:"150px"}},{default:t(()=>[e(d,{label:"启用",value:1}),e(d,{label:"禁用",value:2})]),_:1},8,["modelValue"])]),_:1}),e(i,{label:"是否付费"},{default:t(()=>[e(h,{modelValue:s(u).isPaid,"onUpdate:modelValue":o[4]||(o[4]=a=>s(u).isPaid=a),placeholder:"请选择",clearable:"",style:{width:"150px"}},{default:t(()=>[e(d,{label:"免费",value:0}),e(d,{label:"付费",value:1})]),_:1},8,["modelValue"])]),_:1}),e(i,{label:"智能体名称"},{default:t(()=>[e(T,{modelValue:s(u).agentName,"onUpdate:modelValue":o[5]||(o[5]=a=>s(u).agentName=a),placeholder:"请输入智能体名称",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(i,{label:"创作者昵称"},{default:t(()=>[e(T,{modelValue:s(u).creatorNickname,"onUpdate:modelValue":o[6]||(o[6]=a=>s(u).creatorNickname=a),placeholder:"请输入创作者昵称",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(i,null,{default:t(()=>[e(b,{type:"primary",onClick:B},{default:t(()=>[n("搜索")]),_:1}),e(b,{onClick:$},{default:t(()=>[n("重置")]),_:1})]),_:1})]),_:1})]),ce((v(),z(Q,{data:s(A),border:"",style:{width:"100%"}},{default:t(()=>[e(p,{prop:"categoryGuid",label:"分类GUID",width:"320"}),e(p,{prop:"agentType",label:"智能体类型",width:"120"},{default:t(a=>[e(S,{type:K(a.row.agentType)},{default:t(()=>[n(g(J(a.row.agentType)),1)]),_:2},1032,["type"])]),_:1}),e(p,{prop:"auditStatus",label:"审核状态",width:"100"},{default:t(a=>[e(S,{type:G(a.row.auditStatus)},{default:t(()=>[n(g(R(a.row.auditStatus)),1)]),_:2},1032,["type"])]),_:1}),e(p,{prop:"status",label:"状态",width:"80"},{default:t(a=>[e(S,{type:a.row.status===1?"success":"danger"},{default:t(()=>[n(g(a.row.status===1?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),e(p,{prop:"isPaid",label:"是否付费",width:"100"},{default:t(a=>[e(S,{type:a.row.isPaid===1?"warning":"success"},{default:t(()=>[n(g(a.row.isPaid===1?"付费":"免费"),1)]),_:2},1032,["type"])]),_:1}),e(p,{prop:"priceText",label:"价格"}),e(p,{prop:"agentName",label:"智能体名称","show-overflow-tooltip":""}),e(p,{prop:"creator.nickname",label:"创作者昵称","show-overflow-tooltip":""}),e(p,{fixed:"right",label:"操作",width:"180"},{default:t(a=>[e(b,{size:"small",type:"primary",onClick:le=>H(a.row),disabled:a.row.auditStatus===2},{default:t(()=>[n(" 审核 ")]),_:2},1032,["onClick","disabled"]),e(b,{size:"small",type:a.row.status===1?"danger":"success",onClick:le=>Z(a.row)},{default:t(()=>[n(g(a.row.status===1?"禁用":"启用"),1)]),_:2},1032,["type","onClick"])]),_:1})]),_:1},8,["data"])),[[te,s(x)]]),c("div",Re,[e(W,{"current-page":s(u).page,"onUpdate:currentPage":o[7]||(o[7]=a=>s(u).page=a),"page-size":s(u).pageSize,"onUpdate:pageSize":o[8]||(o[8]=a=>s(u).pageSize=a),"page-sizes":[10,20,50,100],total:s(k),layout:"total, sizes, prev, pager, next, jumper","prev-text":"上一页","next-text":"下一页",onSizeChange:j,onCurrentChange:q},null,8,["current-page","page-size","total"])])]),_:1})]),_:1}),e(ae,{title:"智能体审核",modelValue:s(f),"onUpdate:modelValue":o[12]||(o[12]=a=>se(f)?f.value=a:null),width:"600px"},{footer:t(()=>[c("span",Ie,[e(b,{onClick:o[11]||(o[11]=a=>f.value=!1)},{default:t(()=>[n("取消")]),_:1}),e(b,{type:"primary",onClick:O},{default:t(()=>[n("确定")]),_:1})])]),default:t(()=>[e(P,{ref_key:"auditFormRef",ref:E,model:s(r),rules:D,"label-width":"100px"},{default:t(()=>[e(i,{label:"智能体信息"},{default:t(()=>[s(y)?(v(),U("div",Ge,[c("p",null,[Pe,n(g(s(y).agentName),1)]),c("p",null,[Le,n(g(s(y).creator.nickname),1)]),c("p",null,[Me,e(S,{type:G(s(y).auditStatus)},{default:t(()=>[n(g(R(s(y).auditStatus)),1)]),_:1},8,["type"])])])):I("",!0)]),_:1}),e(i,{label:"审核状态",prop:"auditStatus"},{default:t(()=>[e(ee,{modelValue:s(r).auditStatus,"onUpdate:modelValue":o[9]||(o[9]=a=>s(r).auditStatus=a)},{default:t(()=>[e(L,{label:2},{default:t(()=>[n("审核通过")]),_:1}),e(L,{label:3},{default:t(()=>[n("审核拒绝")]),_:1})]),_:1},8,["modelValue"])]),_:1}),s(r).auditStatus===3?(v(),z(i,{key:0,label:"拒绝原因",prop:"auditRemark"},{default:t(()=>[e(T,{modelValue:s(r).auditRemark,"onUpdate:modelValue":o[10]||(o[10]=a=>s(r).auditRemark=a),type:"textarea",rows:4,placeholder:"请输入拒绝原因",maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1})):I("",!0)]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}});const Xe=ze(De,[["__scopeId","data-v-56242a7b"]]);export{Xe as default};
