<script setup lang="ts">
import { useAdminCommonStore } from '@/stores/adminCommon';
import { useRoute, onBeforeRouteUpdate } from 'vue-router';
import { getMerchantRolesMenusApi } from '@/api';

const route = useRoute();
const store = useAdminCommonStore();
const isCollapse = computed(() => {
  return store.isCollapse;
});
const defaultActive = ref(route.path);
// 菜单项接口定义
interface MenuItem {
  name: string;
  intro: string;
  action: string;
  type: string;
  router: string;
  icon: string;
  sort: number;
  sysId: number;
  belongTo: string;
  parentId: number;
  isShow: number;
  isOpenMenu: number;
  createTime: number;
  updateTime: number;
  deletedAt: null | string;
  modifyTime: string;
  children?: MenuItem[];
}
// 定义动态菜单数据
const menuList = ref<MenuItem[]>([]);
onBeforeRouteUpdate((to, from) => {
  defaultActive.value = to.path;
});
const getMerchantRolesMenus = async () => {
  try {
    const res = await getMerchantRolesMenusApi();
    menuList.value = res.data || [];
  } catch (error) {
    console.error('获取菜单失败:', error);
  }
};
getMerchantRolesMenus();
// i-ep-/
const roterList = reactive([
  {
    title: '商家管理',
    id: 'TENANT',
    icon: 'Shop',
    children: [
      {
        name: 'tenantList',
        path: '/tenant/list',
        meta: {
          title: '商家信息',
        },
      },
      {
        name: 'goodsList',
        path: '/goods/list',
        meta: {
          title: '点数订单列表',
        },
      },
      {
        name: 'digitGoodsList',
        path: '/digit-goods/list',
        meta: {
          title: '数字人订单',
        },
      },
      {
        name: 'aiRole',
        path: '/role/rolelist',
        meta: {
          title: 'Ai好友列表',
        },
      },
      {
        name: 'sceneList',
        path: '/scene/scenelist',
        meta: {
          title: '场景列表',
        },
      },
      {
        name: 'cardlist',
        path: '/card/cardlist',
        meta: {
          title: '分身列表',
        },
      },
      {
        name: 'tenantExpoList',
        path: '/tenant/tenantExpoList',
        meta: {
          title: 'AI主页列表',
        },
      },
    ],
  },
  {
    title: '用户中心',
    id: 'VIP',
    icon: 'UserFilled',
    children: [
      {
        name: 'vipList',
        path: '/vip/list',
        meta: {
          title: '会员列表',
        },
      },
      {
        name: 'digitList',
        path: '/vip/digitList',
        meta: {
          title: '数字人列表',
        },
      },
      {
        name: 'collectList',
        path: '/collect/list',
        meta: {
          title: '聊天收藏',
        },
      },
      {
        name: 'drawlist',
        path: '/tool/drawlist',
        meta: {
          title: '绘画列表',
        },
      },
      {
        name: 'posterlist',
        path: '/poster/list',
        meta: {
          title: '海报列表',
        },
      },
      {
        name: 'videolist',
        path: '/tool/videolist',
        meta: {
          title: '视频列表',
        },
      },
    ],
  },
  {
    title: '管理中心',
    id: 'ADMIN',
    icon: 'Operation',
    children: [
      {
        name: 'admin',
        path: '/admin/list',
        meta: {
          title: '管理员列表',
        },
      },
      {
        name: 'roleList',
        path: '/permissionrole/list',
        meta: {
          title: '角色列表',
        },
      },
    ],
  },
  {
    title: '系统设置',
    id: 'SETTING',
    icon: 'Tools',
    children: [
      {
        name: 'setting',
        path: '/setting/matter',
        meta: {
          title: '素材管理',
        },
      },
    ],
  },
  {
    title: '分享管理',
    id: 'SHARE',
    icon: 'Share',
    children: [
      {
        name: 'share',
        path: '/share/navlist',
        meta: {
          title: '首页链接配置',
        },
      },
    ],
  },
  {
    title: '知识库管理',
    id: 'KNOWLEDGE',
    icon: 'WalletFilled',
    children: [
      {
        name: 'knowledgelist',
        path: '/knowledge/knowledgelist',
        meta: {
          title: '知识库列表',
        },
      },
    ],
  },
  {
    title: '会员卡',
    id: 'VIPCARD',
    icon: 'Medal',
    children: [
      {
        name: 'bigVipConfig',
        path: '/vip-card/bigVipConfig',
        meta: {
          title: '会员卡配置',
        },
      },
      {
        name: 'vipcardlist',
        path: '/vip-card/list',
        meta: {
          title: '会员卡列表',
        },
      },
      {
        name: 'cardApplylist',
        path: '/vip-card/applylist',
        meta: {
          title: '大会员申请列表',
        },
      },
      {
        name: 'cardUserlist',
        path: '/vip-card/userlist',
        meta: {
          title: '会员列表',
        },
      },
      {
        name: 'cardOrderList',
        path: '/vip-card/orderlist',
        meta: {
          title: '会员订单列表',
        },
      },
      {
        name: 'cardWithdrawalList',
        path: '/vip-card/withdrawalList',
        meta: {
          title: '大会员提现列表',
        },
      },
    ],
  },
  {
    title: '商协通AI主页',
    id: 'EXPO',
    icon: 'Guide',
    children: [
      {
        name: 'expo',
        path: '/expo/expoList',
        meta: {
          title: 'AI主页列表',
        },
      },
      {
        name: 'myExpoList',
        path: '/expo/myexpoList',
        meta: {
          title: '我的AI主页',
        },
      },
      {
        name: 'redeemCodes',
        path: '/expo/redeemCodes',
        meta: {
          title: 'AI主页邀请码列表',
        },
      },
    ],
  },
  {
    title: 'PC智能体',
    id: 'AGENT',
    icon: 'Guide',
    children: [
      {
        name: 'agent',
        path: '/agents/list',
        meta: {
          title: '智能体列表',
        },
      },

    ],
  },
  {
    title: '付费智能体管理',
    id: 'PAYAGENT',
    icon: 'Guide',
    children: [
      {
        name: 'payAgentClassList',
        path: '/pay-agent/classlist',
        meta: {
          title: '智能体分类列表',
        },
      },
      {
        name: 'userAgentList',
        path: '/pay-agent/useragentlist',
        meta: {
          title: '用户智能体列表',
        },
      },
      {
        name: 'payAgentConfig',
        path: '/pay-agent/config',
        meta: {
          title: '付费智能体配置',
        },
      },
      {
        name: 'vipAgentList',
        path: '/pay-agent/vipagentlist',
        meta: {
          title: '会员订单列表',
        },
      },
    ],
  },
  {
    title: '付费智能体套餐管理',
    id: 'PAYAGENTPACKAGE',
    icon: 'Guide',
    children: [
      {
        name: 'vipBasePackageList',
        path: '/pay-agent-packages/vipbasepackageList',
        meta: {
          title: '会员基础套餐列表',
        },
      },
    ],
  },
]);
</script>

<!-- <template>
  <div class="f-menu">
    <el-scrollbar>
      <el-menu :default-active="defaultActive" unique-opened router :collapse="isCollapse" :collapse-transition="false">
        <el-sub-menu :index="item.id" v-for="item in roterList" :key="item.id">
          <template #title>
            <el-icon>
              <component :is="item.icon"></component>
            </el-icon>
            <span>{{ item.title }}</span>
          </template>
          <el-menu-item :index="subItme.path" :route="subItme.path" v-for="subItme in item.children"
            :key="subItme.path">
            <span>{{ subItme.meta.title }}</span>
          </el-menu-item>
        </el-sub-menu>
      </el-menu>
    </el-scrollbar>
  </div>
</template> -->
<template>
  <div class="f-menu">
    <el-scrollbar>
      <el-menu :default-active="defaultActive" unique-opened router :collapse="isCollapse" :collapse-transition="false">
        <el-sub-menu v-for="item in menuList" :key="item.sysId" :index="item.router">
          <template #title>
            <el-icon>
              <component :is="item.icon"></component>
            </el-icon>
            <span>{{ item.name }}</span>
          </template>

          <el-menu-item v-for="subItem in item.children" :key="subItem.sysId" :index="subItem.router">
            <span>{{ subItem.name }}</span>
          </el-menu-item>
        </el-sub-menu>
      </el-menu>
    </el-scrollbar>
  </div>
</template>
<style scoped lang="scss">
.f-menu {
  background-color: #fff;
  height: calc(100vh - 60px);
  // border-right: 1px solid #eee;
  box-shadow: 5px 0 10px -5px #eee;
  overflow-y: auto;
  overflow-x: hidden;
}

.el-menu {
  // width: 250px;
  background-color: #fff;
  border-right: none;

  &.el-menu--collapse {
    width: 60px;
  }
}

.el-menu-item {
  &.is-active {
    background-color: #ecf5ff;
  }
}
</style>
