import{d as q,r,a as h,c as u,b as e,w as a,aK as z,e as D,o as d,f as m,h as n,G,H as M,aC as b,U as k,i as O,T as U,I as $,k as A,l as H,p as K,q as Y,s as Z,v as j,y as J,x as Q,Y as W,Z as X,_ as ee}from"./index-8fc71dda.js";/* empty css                   *//* empty css                      *//* empty css                 *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                     */const te={class:"header-box"},ae={class:"mode-box"},oe=["onClick"],ne=["onClick"],se=q({__name:"VideoListView",setup(le){r([]);const l=h({merchantGuid:"",nickname:"",status:"",page:1,pageSize:13}),f=r(0),v=r([]);let c=r(!1);const _=async()=>{c.value=!0;let t=await z(l);f.value=t.data.total,t.data.data.forEach(s=>{switch(s.orderStatus){case"success":s.status="执行成功";break;case"fail":s.status="执行失败";break;case"wait":s.status="等待执行";break;case"doing":s.status="正在执行";break}}),v.value=t.data.data,c.value=!1},y=async t=>{l.page=t,_()};let w=r("");const C=h([{id:0,type:"wait",name:"等待执行"},{id:1,type:"doing",name:"正在执行"},{id:2,type:"fail",name:"执行失败"},{id:3,type:"success",name:"执行成功"}]),V=t=>{w.value=t,l.status=t},E=()=>{_()},L=t=>{t.videoResult.length>0&&window.open(t.videoResult,"_blank")};return _(),(t,s)=>{const x=A,p=H,I=K,R=Y,i=Z,B=j,S=J,T=Q,F=W,N=D,P=X;return d(),u("div",null,[e(N,{class:"wrapper"},{default:a(()=>[e(S,null,{default:a(()=>[m("div",te,[e(R,{inline:!0,model:n(l),class:"demo-form-inline"},{default:a(()=>[e(p,{label:"用户昵称"},{default:a(()=>[e(x,{modelValue:n(l).nickname,"onUpdate:modelValue":s[0]||(s[0]=o=>n(l).nickname=o),placeholder:"用户昵称"},null,8,["modelValue"])]),_:1}),e(p,{label:"场景类型"},{default:a(()=>[m("div",ae,[(d(!0),u(G,null,M(n(C),(o,g)=>(d(),u("div",{class:b(["item",{active:n(w)===o.type}]),key:g,onClick:ie=>V(o.type)},k(o.name),11,oe))),128))])]),_:1}),e(p,null,{default:a(()=>[e(I,{type:"primary",onClick:E},{default:a(()=>[O("搜索")]),_:1})]),_:1})]),_:1},8,["model"])]),U((d(),$(B,{data:n(v),border:"",style:{width:"100%"}},{default:a(()=>[e(i,{prop:"sysId",label:"id",width:"80"}),e(i,{prop:"orderNo",label:"订单编号",width:"300"}),e(i,{prop:"userPrompt","show-overflow-tooltip":!0,label:"提示词",width:"300"}),e(i,{prop:"videoResult","show-overflow-tooltip":!0,label:"视频",width:"100"},{default:a(o=>[m("div",{onClick:g=>L(o.row),class:b(["video-link",{active:o.row.videoResult}])},k(o.row.videoResult?"点击查看":""),11,ne)]),_:1}),e(i,{prop:"payPoint",label:"支付金额",width:"150"}),e(i,{prop:"status",label:"状态",width:"150"}),e(i,{prop:"modifyTime",label:"时间",width:"250"})]),_:1},8,["data"])),[[P,n(c)]])]),_:1}),e(F,null,{default:a(()=>[e(T,{background:"",layout:"prev,pager, next",total:n(f),"current-page":n(l).page,onCurrentChange:y},null,8,["total","current-page"])]),_:1})]),_:1})])}}});const ve=ee(se,[["__scopeId","data-v-dc949227"]]);export{ve as default};
