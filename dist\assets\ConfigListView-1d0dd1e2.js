import{d as O,r as c,a6 as P,c as V,b as e,w as t,cb as Q,E as x,e as W,o as f,T as X,h as l,I as U,f as r,i as d,A as q,b4 as Y,G as ee,H as te,U as ae,cc as le,p as ie,s as oe,v as ne,cd as re,ce as se,a3 as de,l as _e,b8 as ue,ba as me,t as pe,k as ce,ae as fe,b6 as ge,q as we,C as ye,y as be,Z as ve,bb as he,bc as Ve,_ as xe}from"./index-8fc71dda.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                     *//* empty css                *//* empty css               *//* empty css                 *//* empty css                  *//* empty css               *//* empty css                        *//* empty css                        */const u=b=>(he("data-v-cab0a304"),b=b(),Ve(),b),Ee={class:"card-header"},Ce=u(()=>r("span",null,"平台配置管理",-1)),Ie=u(()=>r("span",{class:"unit"},"%",-1)),Ae=u(()=>r("span",{class:"unit"},"%",-1)),ke=u(()=>r("span",{class:"unit"},"%",-1)),Ue=u(()=>r("span",{class:"unit"},"元",-1)),qe=u(()=>r("span",{class:"unit"},"个",-1)),De=u(()=>r("span",{class:"unit"},"个",-1)),Te=u(()=>r("span",{class:"unit"},"元",-1)),Se=u(()=>r("span",{class:"unit"},"元",-1)),Be={class:"model-config"},Le={class:"model-input"},Me={key:0,class:"model-list"},Ne={key:1,class:"empty-models"},Re={class:"dialog-footer"},Fe=O({__name:"ConfigListView",setup(b){const E=c(!1),C=c(!1);let a=c({platform_fee_rate:0,commission_rate:0,auto_audit_enabled:!1,daily_create_limit:0,total_create_limit:0,max_agent_price:0,daily_withdraw_limit:0,single_withdraw_limit:0,withdraw_fee_rate:0,supported_ai_models:[]});const g=c(!1),D=P(()=>[{category:"费率配置",name:"平台手续费比例",value:a.value.platform_fee_rate,unit:"%",description:"平台收取的手续费比例"},{category:"费率配置",name:"邀请佣金比例",value:a.value.commission_rate,unit:"%",description:"邀请用户获得的佣金比例"},{category:"费率配置",name:"提现手续费比例",value:a.value.withdraw_fee_rate,unit:"%",description:"用户提现时收取的手续费比例"},{category:"智能体配置",name:"自动审批开关",value:a.value.auto_audit_enabled?"开启":"关闭",unit:"",description:"智能体提交后是否自动审批通过"},{category:"智能体配置",name:"最大收费金额",value:a.value.max_agent_price,unit:"元",description:"智能体可设置的最大收费金额"},{category:"智能体配置",name:"单日创建限制",value:a.value.daily_create_limit,unit:"个",description:"用户每天最多可创建的智能体数量"},{category:"智能体配置",name:"累计创建限制",value:a.value.total_create_limit,unit:"个",description:"用户累计最多可创建的智能体数量"},{category:"提现配置",name:"单日提现限额",value:a.value.daily_withdraw_limit,unit:"元",description:"用户每天最多可提现的金额"},{category:"提现配置",name:"单笔提现限额",value:a.value.single_withdraw_limit,unit:"元",description:"用户单次提现的最大金额"},{category:"AI模型配置",name:"支持的AI模型",value:a.value.supported_ai_models.length>0?a.value.supported_ai_models.join(", "):"暂无配置",unit:"",description:"平台支持的AI模型列表"}]),I=c(),T={platform_fee_rate:[{required:!0,message:"请输入平台手续费比例",trigger:"blur"},{type:"number",min:0,max:100,message:"比例范围为0-100",trigger:"blur"}],commission_rate:[{required:!0,message:"请输入邀请佣金比例",trigger:"blur"},{type:"number",min:0,max:100,message:"比例范围为0-100",trigger:"blur"}],daily_create_limit:[{required:!0,message:"请输入单日创建限制",trigger:"blur"},{type:"number",min:1,message:"最少为1个",trigger:"blur"}],total_create_limit:[{required:!0,message:"请输入累计创建限制",trigger:"blur"},{type:"number",min:1,message:"最少为1个",trigger:"blur"}],max_agent_price:[{required:!0,message:"请输入最大收费金额",trigger:"blur"},{type:"number",min:0,message:"金额不能为负数",trigger:"blur"}],daily_withdraw_limit:[{required:!0,message:"请输入单日提现限额",trigger:"blur"},{type:"number",min:0,message:"金额不能为负数",trigger:"blur"}],single_withdraw_limit:[{required:!0,message:"请输入单笔提现限额",trigger:"blur"},{type:"number",min:0,message:"金额不能为负数",trigger:"blur"}],withdraw_fee_rate:[{required:!0,message:"请输入提现手续费比例",trigger:"blur"},{type:"number",min:0,max:100,message:"比例范围为0-100",trigger:"blur"}]},S=async()=>{E.value=!0;try{let n=await Q({merchantGuid:""});n.data&&(a.value={platform_fee_rate:n.data.platform_fee_rate||0,commission_rate:n.data.commission_rate||0,auto_audit_enabled:n.data.auto_audit_enabled||!1,daily_create_limit:n.data.daily_create_limit||0,total_create_limit:n.data.total_create_limit||0,max_agent_price:n.data.max_agent_price||0,daily_withdraw_limit:n.data.daily_withdraw_limit||0,single_withdraw_limit:n.data.single_withdraw_limit||0,withdraw_fee_rate:n.data.withdraw_fee_rate||0,supported_ai_models:n.data.supported_ai_models||[]})}catch{x.error("获取配置数据失败")}finally{E.value=!1}},B=()=>{g.value=!0},L=async()=>{I.value&&await I.value.validate(async n=>{if(n){C.value=!0;try{await le({merchantGuid:"",...a.value}),x.success("配置保存成功")}catch{x.error("配置保存失败")}finally{C.value=!1}}})},p=c(""),A=()=>{p.value.trim()&&(a.value.supported_ai_models.includes(p.value.trim())?x.warning("该模型已存在"):(a.value.supported_ai_models.push(p.value.trim()),p.value=""))},M=n=>{a.value.supported_ai_models.splice(n,1)};return S(),(n,i)=>{const v=ie,w=oe,N=ne,R=re,h=se,m=de,s=_e,_=ue,y=me,F=pe,G=ce,K=fe,$=ge,j=we,H=ye,Z=be,z=W,J=ve;return f(),V("div",null,[e(z,{class:"wrapper"},{default:t(()=>[e(Z,null,{default:t(()=>[X((f(),U(R,{class:"config-card"},{header:t(()=>[r("div",Ee,[Ce,e(v,{type:"primary",onClick:B},{default:t(()=>[d(" 编辑配置 ")]),_:1})])]),default:t(()=>[e(N,{data:l(D),border:"",style:{width:"100%"}},{default:t(()=>[e(w,{prop:"category",label:"配置分类",width:"150"}),e(w,{prop:"name",label:"配置项",width:"200"}),e(w,{prop:"value",label:"当前值"}),e(w,{prop:"unit",label:"单位",width:"80"}),e(w,{prop:"description",label:"说明","show-overflow-tooltip":""})]),_:1},8,["data"])]),_:1})),[[J,l(E)]]),e(H,{title:"编辑平台配置",modelValue:l(g),"onUpdate:modelValue":i[11]||(i[11]=o=>q(g)?g.value=o:null),width:"800px"},{footer:t(()=>[r("span",Re,[e(v,{onClick:i[10]||(i[10]=o=>g.value=!1)},{default:t(()=>[d("取消")]),_:1}),e(v,{type:"primary",onClick:L,loading:l(C)},{default:t(()=>[d("保存")]),_:1},8,["loading"])])]),default:t(()=>[e(j,{ref_key:"formRef",ref:I,model:l(a),rules:T,"label-width":"150px",class:"config-form"},{default:t(()=>[e(h,{"content-position":"left"},{default:t(()=>[d("费率配置")]),_:1}),e(y,{gutter:20},{default:t(()=>[e(_,{span:12},{default:t(()=>[e(s,{label:"平台手续费比例",prop:"platform_fee_rate"},{default:t(()=>[e(m,{modelValue:l(a).platform_fee_rate,"onUpdate:modelValue":i[0]||(i[0]=o=>l(a).platform_fee_rate=o),min:0,max:100,precision:2,style:{width:"100%"}},null,8,["modelValue"]),Ie]),_:1})]),_:1}),e(_,{span:12},{default:t(()=>[e(s,{label:"邀请佣金比例",prop:"commission_rate"},{default:t(()=>[e(m,{modelValue:l(a).commission_rate,"onUpdate:modelValue":i[1]||(i[1]=o=>l(a).commission_rate=o),min:0,max:100,precision:2,style:{width:"100%"}},null,8,["modelValue"]),Ae]),_:1})]),_:1})]),_:1}),e(y,{gutter:20},{default:t(()=>[e(_,{span:12},{default:t(()=>[e(s,{label:"提现手续费比例",prop:"withdraw_fee_rate"},{default:t(()=>[e(m,{modelValue:l(a).withdraw_fee_rate,"onUpdate:modelValue":i[2]||(i[2]=o=>l(a).withdraw_fee_rate=o),min:0,max:100,precision:2,style:{width:"100%"}},null,8,["modelValue"]),ke]),_:1})]),_:1})]),_:1}),e(h,{"content-position":"left"},{default:t(()=>[d("智能体配置")]),_:1}),e(y,{gutter:20},{default:t(()=>[e(_,{span:12},{default:t(()=>[e(s,{label:"自动审批开关"},{default:t(()=>[e(F,{modelValue:l(a).auto_audit_enabled,"onUpdate:modelValue":i[3]||(i[3]=o=>l(a).auto_audit_enabled=o),"active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"])]),_:1})]),_:1}),e(_,{span:12},{default:t(()=>[e(s,{label:"最大收费金额",prop:"max_agent_price"},{default:t(()=>[e(m,{modelValue:l(a).max_agent_price,"onUpdate:modelValue":i[4]||(i[4]=o=>l(a).max_agent_price=o),min:0,precision:2,style:{width:"100%"}},null,8,["modelValue"]),Ue]),_:1})]),_:1})]),_:1}),e(y,{gutter:20},{default:t(()=>[e(_,{span:12},{default:t(()=>[e(s,{label:"单日创建限制",prop:"daily_create_limit"},{default:t(()=>[e(m,{modelValue:l(a).daily_create_limit,"onUpdate:modelValue":i[5]||(i[5]=o=>l(a).daily_create_limit=o),min:1,style:{width:"100%"}},null,8,["modelValue"]),qe]),_:1})]),_:1}),e(_,{span:12},{default:t(()=>[e(s,{label:"累计创建限制",prop:"total_create_limit"},{default:t(()=>[e(m,{modelValue:l(a).total_create_limit,"onUpdate:modelValue":i[6]||(i[6]=o=>l(a).total_create_limit=o),min:1,style:{width:"100%"}},null,8,["modelValue"]),De]),_:1})]),_:1})]),_:1}),e(h,{"content-position":"left"},{default:t(()=>[d("提现配置")]),_:1}),e(y,{gutter:20},{default:t(()=>[e(_,{span:12},{default:t(()=>[e(s,{label:"单日提现限额",prop:"daily_withdraw_limit"},{default:t(()=>[e(m,{modelValue:l(a).daily_withdraw_limit,"onUpdate:modelValue":i[7]||(i[7]=o=>l(a).daily_withdraw_limit=o),min:0,precision:2,style:{width:"100%"}},null,8,["modelValue"]),Te]),_:1})]),_:1}),e(_,{span:12},{default:t(()=>[e(s,{label:"单笔提现限额",prop:"single_withdraw_limit"},{default:t(()=>[e(m,{modelValue:l(a).single_withdraw_limit,"onUpdate:modelValue":i[8]||(i[8]=o=>l(a).single_withdraw_limit=o),min:0,precision:2,style:{width:"100%"}},null,8,["modelValue"]),Se]),_:1})]),_:1})]),_:1}),e(h,{"content-position":"left"},{default:t(()=>[d("AI模型配置")]),_:1}),e(s,{label:"支持的AI模型"},{default:t(()=>[r("div",Be,[r("div",Le,[e(G,{modelValue:l(p),"onUpdate:modelValue":i[9]||(i[9]=o=>q(p)?p.value=o:null),placeholder:"请输入AI模型名称",onKeyup:Y(A,["enter"]),style:{width:"300px"}},null,8,["modelValue","onKeyup"]),e(v,{type:"primary",onClick:A,style:{"margin-left":"10px"}},{default:t(()=>[d("添加")]),_:1})]),l(a).supported_ai_models.length>0?(f(),V("div",Me,[(f(!0),V(ee,null,te(l(a).supported_ai_models,(o,k)=>(f(),U(K,{key:k,closable:"",onClose:Ge=>M(k),style:{margin:"5px 5px 0 0"}},{default:t(()=>[d(ae(o),1)]),_:2},1032,["onClose"]))),128))])):(f(),V("div",Ne,[e($,{type:"info"},{default:t(()=>[d("暂无配置的AI模型")]),_:1})]))])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])]),_:1})]),_:1})])}}});const Ye=xe(Fe,[["__scopeId","data-v-cab0a304"]]);export{Ye as default};
