<script setup lang="ts">
import {
  getAiAgentConfigApi,
  updateAiAgentConfigApi
} from '@/api';
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';

// 加载状态
const loading = ref(false);
const saving = ref(false);

// 数据类型定义
interface ConfigData {
  platform_fee_rate: number;
  commission_rate: number;
  auto_audit_enabled: boolean;
  daily_create_limit: number;
  total_create_limit: number;
  max_agent_price: number;
  daily_withdraw_limit: number;
  single_withdraw_limit: number;
  withdraw_fee_rate: number;
  supported_ai_models: string[];
}


let configData = ref<ConfigData>({
  platform_fee_rate: 0,
  commission_rate: 0,
  auto_audit_enabled: false,
  daily_create_limit: 0,
  total_create_limit: 0,
  max_agent_price: 0,
  daily_withdraw_limit: 0,
  single_withdraw_limit: 0,
  withdraw_fee_rate: 0,
  supported_ai_models: []
});

// 当前选中的商户
// const selectedMerchantGuid = ref('');

// 编辑弹窗状态
const editDialogVisible = ref(false);

// 表格数据
const configTableData = computed(() => {
  return [
    {
      category: '费率配置',
      name: '平台手续费比例',
      value: configData.value.platform_fee_rate,
      unit: '%',
      description: '平台收取的手续费比例'
    },
    {
      category: '费率配置',
      name: '邀请佣金比例',
      value: configData.value.commission_rate,
      unit: '%',
      description: '邀请用户获得的佣金比例'
    },
    {
      category: '费率配置',
      name: '提现手续费比例',
      value: configData.value.withdraw_fee_rate,
      unit: '%',
      description: '用户提现时收取的手续费比例'
    },
    {
      category: '智能体配置',
      name: '自动审批开关',
      value: configData.value.auto_audit_enabled ? '开启' : '关闭',
      unit: '',
      description: '智能体提交后是否自动审批通过'
    },
    {
      category: '智能体配置',
      name: '最大收费金额',
      value: configData.value.max_agent_price,
      unit: '元',
      description: '智能体可设置的最大收费金额'
    },
    {
      category: '智能体配置',
      name: '单日创建限制',
      value: configData.value.daily_create_limit,
      unit: '个',
      description: '用户每天最多可创建的智能体数量'
    },
    {
      category: '智能体配置',
      name: '累计创建限制',
      value: configData.value.total_create_limit,
      unit: '个',
      description: '用户累计最多可创建的智能体数量'
    },
    {
      category: '提现配置',
      name: '单日提现限额',
      value: configData.value.daily_withdraw_limit,
      unit: '元',
      description: '用户每天最多可提现的金额'
    },
    {
      category: '提现配置',
      name: '单笔提现限额',
      value: configData.value.single_withdraw_limit,
      unit: '元',
      description: '用户单次提现的最大金额'
    },
    {
      category: 'AI模型配置',
      name: '支持的AI模型',
      value: configData.value.supported_ai_models.length > 0
        ? configData.value.supported_ai_models.join(', ')
        : '暂无配置',
      unit: '',
      description: '平台支持的AI模型列表'
    }
  ];
});

// 表单引用和验证规则
const formRef = ref<FormInstance>();
const rules: FormRules = {
  platform_fee_rate: [
    { required: true, message: '请输入平台手续费比例', trigger: 'blur' },
    { type: 'number', min: 0, max: 100, message: '比例范围为0-100', trigger: 'blur' }
  ],
  commission_rate: [
    { required: true, message: '请输入邀请佣金比例', trigger: 'blur' },
    { type: 'number', min: 0, max: 100, message: '比例范围为0-100', trigger: 'blur' }
  ],
  daily_create_limit: [
    { required: true, message: '请输入单日创建限制', trigger: 'blur' },
    { type: 'number', min: 1, message: '最少为1个', trigger: 'blur' }
  ],
  total_create_limit: [
    { required: true, message: '请输入累计创建限制', trigger: 'blur' },
    { type: 'number', min: 1, message: '最少为1个', trigger: 'blur' }
  ],
  max_agent_price: [
    { required: true, message: '请输入最大收费金额', trigger: 'blur' },
    { type: 'number', min: 0, message: '金额不能为负数', trigger: 'blur' }
  ],
  daily_withdraw_limit: [
    { required: true, message: '请输入单日提现限额', trigger: 'blur' },
    { type: 'number', min: 0, message: '金额不能为负数', trigger: 'blur' }
  ],
  single_withdraw_limit: [
    { required: true, message: '请输入单笔提现限额', trigger: 'blur' },
    { type: 'number', min: 0, message: '金额不能为负数', trigger: 'blur' }
  ],
  withdraw_fee_rate: [
    { required: true, message: '请输入提现手续费比例', trigger: 'blur' },
    { type: 'number', min: 0, max: 100, message: '比例范围为0-100', trigger: 'blur' }
  ]
};





// 获取配置数据
const getConfigData = async () => {

  loading.value = true;
  try {
    let res = await getAiAgentConfigApi({
      merchantGuid: ''
    });

    if (res.data) {
      configData.value = {
        platform_fee_rate: res.data.platform_fee_rate || 0,
        commission_rate: res.data.commission_rate || 0,
        auto_audit_enabled: res.data.auto_audit_enabled || false,
        daily_create_limit: res.data.daily_create_limit || 0,
        total_create_limit: res.data.total_create_limit || 0,
        max_agent_price: res.data.max_agent_price || 0,
        daily_withdraw_limit: res.data.daily_withdraw_limit || 0,
        single_withdraw_limit: res.data.single_withdraw_limit || 0,
        withdraw_fee_rate: res.data.withdraw_fee_rate || 0,
        supported_ai_models: res.data.supported_ai_models || []
      };
    }
  } catch (error) {
    ElMessage.error('获取配置数据失败');
  } finally {
    loading.value = false;
  }
};

// 商户切换
const handleMerchantChange = () => {
  getConfigData();
};

// 打开编辑弹窗
const handleEdit = () => {
  editDialogVisible.value = true;
};

// 保存配置
const handleSave = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async (valid) => {
    if (valid) {
      saving.value = true;
      try {
        await updateAiAgentConfigApi({
          merchantGuid: '',
          ...configData.value
        });

        ElMessage.success('配置保存成功');
      } catch (error) {
        ElMessage.error('配置保存失败');
      } finally {
        saving.value = false;
      }
    }
  });
};

// AI模型管理
const newModel = ref('');

const addModel = () => {
  if (newModel.value.trim()) {
    if (!configData.value.supported_ai_models.includes(newModel.value.trim())) {
      configData.value.supported_ai_models.push(newModel.value.trim());
      newModel.value = '';
    } else {
      ElMessage.warning('该模型已存在');
    }
  }
};

const removeModel = (index: number) => {
  configData.value.supported_ai_models.splice(index, 1);
};

// 初始化
getConfigData();
</script>

<template>
  <div>
    <el-container class="wrapper">
      <el-main>
        <!-- 配置表格 -->
        <el-card class="config-card" v-loading="loading">
          <template #header>
            <div class="card-header">
              <span>平台配置管理</span>
              <el-button type="primary" @click="handleEdit">
                编辑配置
              </el-button>
            </div>
          </template>

          <!-- 配置数据表格 -->
          <el-table :data="configTableData" border style="width: 100%">
            <el-table-column prop="category" label="配置分类" width="150" />
            <el-table-column prop="name" label="配置项" width="200" />
            <el-table-column prop="value" label="当前值" />
            <el-table-column prop="unit" label="单位" width="80" />
            <el-table-column prop="description" label="说明" show-overflow-tooltip />
          </el-table>
        </el-card>

        <!-- 编辑配置弹窗 -->
        <el-dialog title="编辑平台配置" v-model="editDialogVisible" width="800px">

          <el-form ref="formRef" :model="configData" :rules="rules" label-width="150px" class="config-form">

            <!-- 费率配置 -->
            <el-divider content-position="left">费率配置</el-divider>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="平台手续费比例" prop="platform_fee_rate">
                  <el-input-number v-model="configData.platform_fee_rate" :min="0" :max="100" :precision="2"
                    style="width: 100%" />
                  <span class="unit">%</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="邀请佣金比例" prop="commission_rate">
                  <el-input-number v-model="configData.commission_rate" :min="0" :max="100" :precision="2"
                    style="width: 100%" />
                  <span class="unit">%</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="提现手续费比例" prop="withdraw_fee_rate">
                  <el-input-number v-model="configData.withdraw_fee_rate" :min="0" :max="100" :precision="2"
                    style="width: 100%" />
                  <span class="unit">%</span>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 智能体配置 -->
            <el-divider content-position="left">智能体配置</el-divider>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="自动审批开关">
                  <el-switch v-model="configData.auto_audit_enabled" active-text="开启" inactive-text="关闭" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="最大收费金额" prop="max_agent_price">
                  <el-input-number v-model="configData.max_agent_price" :min="0" :precision="2" style="width: 100%" />
                  <span class="unit">元</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="单日创建限制" prop="daily_create_limit">
                  <el-input-number v-model="configData.daily_create_limit" :min="1" style="width: 100%" />
                  <span class="unit">个</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="累计创建限制" prop="total_create_limit">
                  <el-input-number v-model="configData.total_create_limit" :min="1" style="width: 100%" />
                  <span class="unit">个</span>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 提现配置 -->
            <el-divider content-position="left">提现配置</el-divider>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="单日提现限额" prop="daily_withdraw_limit">
                  <el-input-number v-model="configData.daily_withdraw_limit" :min="0" :precision="2"
                    style="width: 100%" />
                  <span class="unit">元</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="单笔提现限额" prop="single_withdraw_limit">
                  <el-input-number v-model="configData.single_withdraw_limit" :min="0" :precision="2"
                    style="width: 100%" />
                  <span class="unit">元</span>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- AI模型配置 -->
            <el-divider content-position="left">AI模型配置</el-divider>
            <el-form-item label="支持的AI模型">
              <div class="model-config">
                <div class="model-input">
                  <el-input v-model="newModel" placeholder="请输入AI模型名称" @keyup.enter="addModel" style="width: 300px" />
                  <el-button type="primary" @click="addModel" style="margin-left: 10px">添加</el-button>
                </div>
                <div class="model-list" v-if="configData.supported_ai_models.length > 0">
                  <el-tag v-for="(model, index) in configData.supported_ai_models" :key="index" closable
                    @close="removeModel(index)" style="margin: 5px 5px 0 0">
                    {{ model }}
                  </el-tag>
                </div>
                <div v-else class="empty-models">
                  <el-text type="info">暂无配置的AI模型</el-text>
                </div>
              </div>
            </el-form-item>
          </el-form>

          <template #footer>
            <span class="dialog-footer">
              <el-button @click="editDialogVisible = false">取消</el-button>
              <el-button type="primary" @click="handleSave" :loading="saving">保存</el-button>
            </span>
          </template>
        </el-dialog>
      </el-main>
    </el-container>
  </div>
</template>

<style scoped lang="scss">
.header-box {
  margin-bottom: 20px;
  padding: 20px;
  background: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.config-card {
  box-shadow: none !important;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    span {
      font-size: 16px;
      font-weight: 500;
    }
  }
}

.config-form {
  .unit {
    margin-left: 8px;
    color: #909399;
    font-size: 14px;
    display: inline-block;
    vertical-align: middle;
  }

  .el-form-item__content {
    display: flex !important;
    align-items: center;
    flex-wrap: nowrap;
  }

  .el-input-number {
    flex: 1;
    min-width: 0;
  }

  .el-divider {
    margin: 30px 0 20px 0;

    .el-divider__text {
      font-weight: 500;
      color: #303133;
    }
  }

  .el-form-item {
    margin-bottom: 20px;
  }
}

.model-config {
  .model-input {
    margin-bottom: 15px;
  }

  .model-list {
    min-height: 40px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;
  }

  .empty-models {
    padding: 20px;
    text-align: center;
    background: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;
  }
}

.wrapper {
  min-height: calc(100vh - 120px);
}
</style>