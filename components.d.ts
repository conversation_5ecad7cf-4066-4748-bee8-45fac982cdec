/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core'

export {}

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    AdminView: typeof import('./src/components/expo/adminView.vue')['default']
    AiRoleListView: typeof import('./src/components/role/AiRoleListView.vue')['default']
    ApplyListView: typeof import('./src/components/vip-card/applyListView.vue')['default']
    BannersView: typeof import('./src/components/tenant/BannersView.vue')['default']
    BaseListView: typeof import('./src/components/pay-agent-packages/BaseListView.vue')['default']
    CardListView: typeof import('./src/components/card/CardListView.vue')['default']
    ChatGoodsView: typeof import('./src/components/tenant/ChatGoodsView.vue')['default']
    ClassListView: typeof import('./src/components/pay-agent/ClassListView.vue')['default']
    ConfigListView: typeof import('./src/components/pay-agent/ConfigListView.vue')['default']
    DigitGoodsListView: typeof import('./src/components/digit-goods/DigitGoodsListView.vue')['default']
    DigitListView: typeof import('./src/components/vip/DigitListView.vue')['default']
    DrawListView: typeof import('./src/components/tool/DrawListView.vue')['default']
    ElAside: typeof import('element-plus/es')['ElAside']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElContainer: typeof import('element-plus/es')['ElContainer']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElFooter: typeof import('element-plus/es')['ElFooter']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElHeader: typeof import('element-plus/es')['ElHeader']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElMain: typeof import('element-plus/es')['ElMain']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopconfirm: typeof import('element-plus/es')['ElPopconfirm']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioButton: typeof import('element-plus/es')['ElRadioButton']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElResult: typeof import('element-plus/es')['ElResult']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElText: typeof import('element-plus/es')['ElText']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTree: typeof import('element-plus/es')['ElTree']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    ExpoBannerView: typeof import('./src/components/expo/expoBannerView.vue')['default']
    ExpoEditView: typeof import('./src/components/expo/expoEditView.vue')['default']
    ExpoFaqListView: typeof import('./src/components/expo/expoFaqListView.vue')['default']
    ExpoListView: typeof import('./src/components/expo/expoListView.vue')['default']
    ExpoMyExpoListView: typeof import('./src/components/expo/expoMyExpoListView.vue')['default']
    FHeader: typeof import('./src/components/layout/FHeader.vue')['default']
    FMenu: typeof import('./src/components/layout/FMenu.vue')['default']
    FTagList: typeof import('./src/components/layout/FTagList.vue')['default']
    GoodsListVIew: typeof import('./src/components/goods/GoodsListVIew.vue')['default']
    IEpArrowDown: typeof import('~icons/ep/arrow-down')['default']
    IEpDelete: typeof import('~icons/ep/delete')['default']
    IEpExpand: typeof import('~icons/ep/expand')['default']
    IEpFold: typeof import('~icons/ep/fold')['default']
    IEpFullScreen: typeof import('~icons/ep/full-screen')['default']
    IEpLoading: typeof import('~icons/ep/loading')['default']
    IEpLock: typeof import('~icons/ep/lock')['default']
    IEpPlus: typeof import('~icons/ep/plus')['default']
    IEpRefresh: typeof import('~icons/ep/refresh')['default']
    IEpUploadFilled: typeof import('~icons/ep/upload-filled')['default']
    IEpUser: typeof import('~icons/ep/user')['default']
    Index: typeof import('./src/components/index/index.vue')['default']
    KnowledgeDetailView: typeof import('./src/components/knowledge/KnowledgeDetailView.vue')['default']
    KnowledgeFileDetailView: typeof import('./src/components/knowledge/KnowledgeFileDetailView.vue')['default']
    KnowledgeListView: typeof import('./src/components/knowledge/KnowledgeListView.vue')['default']
    KnowledgeTestView: typeof import('./src/components/knowledge/KnowledgeTestView.vue')['default']
    ListView: typeof import('./src/components/admin/ListView.vue')['default']
    MatterListView: typeof import('./src/components/matter/MatterListView.vue')['default']
    NavListView: typeof import('./src/components/share/NavListView.vue')['default']
    OrderListView: typeof import('./src/components/vip-card/orderListView.vue')['default']
    PosterListView: typeof import('./src/components/poster/PosterListView.vue')['default']
    RedeemCodes: typeof import('./src/components/expo/redeemCodes.vue')['default']
    RoleListView: typeof import('./src/components/permission/RoleListView.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SceneListView: typeof import('./src/components/scene/SceneListView.vue')['default']
    SetSystemInfoView: typeof import('./src/components/tenant/SetSystemInfoView.vue')['default']
    TenantListView: typeof import('./src/components/tenant/TenantListView.vue')['default']
    UserAgentList: typeof import('./src/components/pay-agent/UserAgentList.vue')['default']
    UserListView: typeof import('./src/components/vip-card/userListView.vue')['default']
    VideoListView: typeof import('./src/components/tool/VideoListView.vue')['default']
    VipConfigView: typeof import('./src/components/vip-card/vipConfigView.vue')['default']
    VipListView: typeof import('./src/components/pay-agent/VipListView.vue')['default']
    WithdrawalListView: typeof import('./src/components/vip-card/withdrawalListView.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
