import{d as U,r,a as v,c as q,b as e,w as a,h as o,A as z,a4 as P,S as R,e as G,C as M,o as w,T as Y,I as Z,f as m,i as b,U as k,k as j,l as H,p as J,q as K,s as O,v as Q,y as W,x as X,Y as $,Z as ee,_ as te}from"./index-8fc71dda.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                      *//* empty css                 *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                     */const ae={class:"hearder-box"},oe={class:"harder"},le={class:"body"},ne=U({__name:"ListView",setup(se){const u=r(0),_=r(!1),l=v({merchantGuid:"",nickname:"",pageSize:10,page:1});let f=r([]),y=r([]),c=r(!1),n=v({title:"",content:"",nickname:""});const p=async()=>{_.value=!0;let t=await P(l);_.value=!1,u.value=t.data.total,f.value=t.data.data},C=async()=>{let t=await R();y.value=t.data},E=async t=>{l.page=t,p()},V=()=>{p()},L=t=>{console.log(t,"itemitem"),n.title=t.chatTitle,n.content=t.chatContent,n.nickname=t.user.nickname,c.value=!0};return p(),C(),(t,d)=>{const x=j,g=H,h=J,T=K,s=O,I=Q,B=W,F=X,S=$,A=G,D=M,N=ee;return w(),q("div",null,[e(A,{class:"wrapper"},{default:a(()=>[Y((w(),Z(B,null,{default:a(()=>[m("div",ae,[e(T,{inline:!0,model:o(l),class:"demo-form-inline"},{default:a(()=>[e(g,{label:"用户昵称"},{default:a(()=>[e(x,{modelValue:o(l).nickname,"onUpdate:modelValue":d[0]||(d[0]=i=>o(l).nickname=i),placeholder:"用户昵称"},null,8,["modelValue"])]),_:1}),e(g,null,{default:a(()=>[e(h,{type:"primary",onClick:V},{default:a(()=>[b("搜索")]),_:1})]),_:1})]),_:1},8,["model"])]),e(I,{data:o(f),border:"",style:{width:"100%"}},{default:a(()=>[e(s,{prop:"user.sysId",label:"用户Id",width:"80"}),e(s,{prop:"user.nickname",label:"用户昵称",width:"180"}),e(s,{prop:"chatTitle","show-overflow-tooltip":!0,label:"收藏标题",width:"220"}),e(s,{prop:"chatContent","show-overflow-tooltip":!0,label:"收藏内容",width:"220"}),e(s,{prop:"modifyTime",label:"收藏时间"}),e(s,{label:"操作"},{default:a(i=>[e(h,{size:"small",type:"primary",onClick:ie=>L(i.row)},{default:a(()=>[b("查看详情")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1})),[[N,o(_)]]),e(S,null,{default:a(()=>[e(F,{background:"",layout:"prev,pager, next",total:o(u),"current-page":o(l).page,onCurrentChange:E},null,8,["total","current-page"])]),_:1})]),_:1}),e(D,{modelValue:o(c),"onUpdate:modelValue":d[1]||(d[1]=i=>z(c)?c.value=i:c=i),title:o(n).nickname},{default:a(()=>[m("div",oe,k(o(n).title),1),m("div",le,k(o(n).content),1)]),_:1},8,["modelValue","title"])])}}});const ve=te(ne,[["__scopeId","data-v-9916defe"]]);export{ve as default};
