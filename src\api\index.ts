import { request, request_xg } from '@/request/request';

//登录
type LoginData = {
  userName: string;
  password: string;
};
// type LoginResult = {
//   code: number;
//   data: any;
//   msg: string;
//   nowTime: number;
//   useTime: string;
// };
export const login = (loginData: LoginData) => {
  return request.post('admin/admin.adminUser/login', loginData);
};
//商户列表
export const getTenantListApi = (data?) => {
  return request.post('merchant/admin.index/index', data);
};
//商户列表分页
export const getTenantListPageApi = (data?) => {
  return request.post('merchant/admin.index/listFilter', data);
};
type ReqCreateTenantType = {
  merchantName: string;
  merchantDesc: string;
  merchantChatCount: number;
};
export const createTenantApi = (data: ReqCreateTenantType) => {
  return request.post('merchant/admin.index/createMerchant', data);
};

type ReqEditTenantType = {
  merchantGuid: string;
  merchantName: string;
  merchantDesc: string;
  merchantChatCount: number;
  status: number;
};
//商户编辑
export const editTenantApi = (data: ReqEditTenantType) => {
  return request.post('merchant/admin.index/editMerchant', data);
};

type ReqChatGoodsType = {
  orderNo: string;
  orderStatus: string;
  merchantGuid: string;
};
//获取聊天商品列表
export const getChatGoodsApi = (data: ReqChatGoodsType) => {
  return request.post('square/admin.chatGoods/orders', data);
};

type ReqDigitType = ReqChatGoodsType;
//数字人订单列表
export const getDigitGoodsApi = (data: ReqDigitType) => {
  return request.post('user/admin.user/channelOrders', data);
};

type ReqVipListType = {
  merchantGuid: string;
  nickname: string;
  parentUid: string;
  mobile: string;
};
//会员列表
export const getVipListApi = (data: ReqVipListType) => {
  return request.post('user/admin.user/index', data);
};

type ReqCollecListType = {
  merchantGuid: string;
  nickname: string;
};
//收藏列表
export const getCollectListApi = (data: ReqCollecListType) => {
  return request.post('user/admin.user/collection', data);
};

//管理员列表
export const getAdminListApi = (data) => {
  return request.post('admin/admin.adminUser/list', data);
};

type ReqDeleteType = {
  guid: string;
};
//删除管理员
export const deleteAdminApi = (data: ReqDeleteType) => {
  return request.post('admin/admin.adminUser/del', data);
};

type ReqAddAdminType = {
  mobile: string;
  userName: string;
  nickname: string;
  password: string;
  adminType: string;
};
//创建管理员
export const createAdminApi = (data: ReqAddAdminType) => {
  return request.post('admin/admin.adminUser/create', data);
};

type ReqEditAdminType = {
  guid: string;
  mobile: string;
  userName: string;
  nickname: string;
  password: string;
};
//修改管理员
export const editAdminApi = (data: ReqEditAdminType) => {
  return request.post('admin/admin.adminUser/edit', data);
};

//获取banner
export const getBannerListApi = (data) => {
  return request.post('merchant/admin.index/banners', data);
};

//删除banner
export const deleteBannerApi = (data) => {
  return request.post('merchant/admin.index/bannerDelete', data);
};

//上传图片
export const uploadImg = (data) => {
  // return request.post('merchant/admin.index/uploadImg', data);
  return request({
    url: 'merchant/admin.index/uploadImg',
    method: 'POST',
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    data: data,
  });
};
//上传视频
export const uploadVideo = (data) => {
  // return request.post('merchant/admin.index/uploadImg', data);
  return request({
    url: 'merchant/admin.index/uploadVideo',
    method: 'POST',
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    data: data,
  });
};
//新建banner
export const createBannerApi = (data) => {
  return request.post('merchant/admin.index/bannerCreate', data);
};

//修改banner
export const updateBannerApi = (data) => {
  return request.post('merchant/admin.index/bannerUpdate', data);
};

type ReqSystemType = {
  merchantGuid: string;
  configGroup: string;
};
//获取系统配置
export const getSystemConfigApi = (data: ReqSystemType) => {
  return request.post('merchant/admin.index/systemConfig', data);
};

type ReqSaveConfigType = {
  merchantGuid: string;
  configKey: string;
  configValue: string;
};
//保存配置
export const saveConfigApi = (data: ReqSaveConfigType) => {
  return request.post('merchant/admin.index/configSave', data);
};

//聊天商品列表
export const getChatsGoodsApi = (data) => {
  return request.post('merchant/admin.index/chatGoods', data);
};

type ReqAddTenantChatType = {
  merchantGuid: string;
  goodsName: string;
  goodsDesc: string;
  chatCount: number;
  price: string;
};
//新增聊天商品
export const createTenantChatApi = (data: ReqAddTenantChatType) => {
  return request.post('merchant/admin.index/chatGoodsCreate', data);
};
type ReqEditTenantChatType = {
  goodsGuid: string;
  goodsName: string;
  goodsDesc: string;
  chatCount: number;
  price: string;
};

//修改聊天商品
export const editTenantChatApi = (data: ReqEditTenantChatType) => {
  return request.post('merchant/admin.index/chatGoodsEdit', data);
};
type ReqDelTenantChatType = {
  goodsGuid: string;
};
//删除聊天商品
export const delTenantChatApi = (data: ReqDelTenantChatType) => {
  return request.post('merchant/admin.index/chatGoodsDel', data);
};

//聊天商品订单结果查询
export const checkTenantChatPayApi = (data) => {
  return request.post('square/admin.chatGoods/queryPay', data);
};

//数字人订单结果查询
export const checkDigitPayApi = (data) => {
  return request.post('user/admin.user/channelOrderQuery', data);
};

//获取商户详情
export const getMerchantDetailApi = () => {
  return request.post('merchant/admin.index/info');
};

//增加用户点数
export const addChatCountApi = (data) => {
  return request.post('user/admin.user/addChatCount', data);
};

//查询订单总金额
export const queryOrderAmountApi = (data) => {
  return request.post('square/admin.chatGoods/orderAmount', data);
};

//获取数字人列表
export const getChannelListApi = (data) => {
  return request.post('user/admin.user/channelList', data);
};

//新增数字人
export const addChannnlApi = (data) => {
  return request.post('user/admin.user/addChannelUser', data);
};
//获取Ai好友列表
export const getRobotlistApi = (data) => {
  return request.post('square/admin.Robot/listRobot', data);
};

type CreateRobotType = {
  name: string;
  merchantGuid: string;
  standing: string;
  signature: string;
  avatar: string;
  chat_expense: number;
  add_expense: number;
  sort: number;
  rule: string;
  welcome: string;
};
//创建Ai好友
export const createRobotApi = (data: CreateRobotType) => {
  return request.post('square/admin.Robot/createRobot', data);
};

type EditRobotType = {
  id: number;
  name: string;
  merchantGuid: string;
  standing: string;
  signature: string;
  avatar: string;
  chat_expense: number;
  add_expense: number;
  sort: number;
  rule: string;
  welcome: string;
};
//创建Ai好友
export const editRobotApi = (data: EditRobotType) => {
  return request.post('square/admin.Robot/editRobot', data);
};

//删除Ai好友
export const delRobotApi = (data) => {
  return request.post('square/admin.Robot/robotDel', data);
};

type SceneListType = {
  merchantGuid: string;
  cate_type: string;
};
//获取场景分类列表
export const getSceneListApi = (data: SceneListType) => {
  return request.post('square/admin.CopywritingCategory/list', data);
};

//获取一级场景分类列表
export const getSceneLastListApi = (data: SceneListType) => {
  return request.post('square/admin.CopywritingCategory/top', data);
};

type createSceneType = {
  pid: number;
  title: string;
  image: string;
  desc: string;
  sort: number;
  status: number;
  chatgtp_content: string;
  cate_type: string;
  merchantGuid: string;
  able_lunci: number;
};
//编辑文案分类
export const editSceneApi = (data: createSceneType) => {
  return request.post('square/admin.CopywritingCategory/edit', data);
};

//创建文案分类
export const createSceneApi = (data: createSceneType) => {
  return request.post('square/admin.CopywritingCategory/create', data);
};

//删除场景分类
export const delSceneApi = (data) => {
  return request.post('square/admin.CopywritingCategory/delete', data);
};

type ImgOrderListType = {
  merchantGuid: string;
  page: number;
  pageSize: number;
  nickname: string;
  status: string;
};
//获取绘画订单列表
export const getImgOrderListApi = (data: ImgOrderListType) => {
  return request.post('user/admin.UserWork/imgOrderList', data);
};

//获取视频订单列表
export const getVideoOrderListApi = (data: ImgOrderListType) => {
  return request.post('user/admin.UserWork/userVideoOrders', data);
};
//工具屋可用模型列表
export const getAbleUseModelsApi = () => {
  return request.post('square/admin.CopywritingCategory/ableUseModels');
};

//创建素材
export const createMaterialApi = (data) => {
  return request.post('merchant/admin.material/create', data);
};

//修改素材
export const updateMaterialApi = (data) => {
  return request.post('merchant/admin.material/update', data);
};

//删除素材
export const deleteMaterialApi = (data) => {
  return request.post('merchant/admin.material/delete', data);
};

//素材列表
export const getMaterialListApi = (data) => {
  return request.post('merchant/admin.material/index', data);
};

//获取场景小程序二维码
export const getCategoryCodeApi = (data) => {
  return request.post('square/admin.CopywritingCategory/categoryCode', data);
};
//获取海报列表
export const getPosterImgApi = (data) => {
  return request.post('user/admin.UserWork/posterImg', data);
};
//获取分身列表
export const getCardListApi = (data) => {
  return request.post('separation/admin.index/index', data);
};
//获取默认配置
export const getDefaultConfigApi = (data) => {
  return request.post('merchant/admin.index/getDefaultConfig', data);
};

//获取首页链接列表
export const getIndexUrlsApi = (data) => {
  return request.post('merchant/admin.IndexUrls/index', data);
};
//创建首页链接列表
export const createIndexUrlsApi = (data) => {
  return request.post('merchant/admin.IndexUrls/create', data);
};
//删除首页链接列表
export const deleteIndexUrlsApi = (data) => {
  return request.post('merchant/admin.IndexUrls/delete', data);
};
//修改首页链接列表
export const updateIndexUrlsApi = (data) => {
  return request.post('merchant/admin.IndexUrls/update', data);
};

//获取知识库列表
export const getKnowLedgeListApi = (data) => {
  return request.post('merchant/admin.KnowledgeBase/index', data);
};

//创建知识库
export const createKnowLedgeApi = (data) => {
  return request.post('merchant/admin.KnowledgeBase/create', data);
};
//更新知识库
export const updateKnowLedgeApi = (data) => {
  return request.post('merchant/admin.KnowledgeBase/update', data);
};
//删除知识库
export const deleteKnowLedgeApi = (data) => {
  return request.post('merchant/admin.KnowledgeBase/delete', data);
};
//商家知识库上传文档
export const addKnowledgeFileApi = (data) => {
  return request.post('merchant/admin.KnowledgeBase/addKnowledgeBaseFile', data);
};
//获取商家知识库文档列表
export const getKnowledgeFileListApi = (data) => {
  return request.post('merchant/admin.KnowledgeBase/knowledgeBaseFileList', data);
};
//删除商家知识库文档
export const delKnowledgeFileApi = (data) => {
  return request.post('merchant/admin.KnowledgeBase/delKnowledgeBaseFile', data);
};
//获取知识库文件片段列表
export const getFileSegmentsApi = (data) => {
  return request.post('merchant/admin.KnowledgeBase/getFileSegments', data);
};
//更新知识库文件片段列表
export const updateFileSegmentsApi = (data) => {
  return request.post('merchant/admin.KnowledgeBase/updateFileSegments', data);
};
//上传文件接口
export const uploadFileApi = (data) => {
  return request({
    url: 'merchant/admin.index/uploadFile',
    method: 'POST',
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    data: data,
  });
};

//商家知识库召回测试
export const KnowledgeSearchTestApi = (data) => {
  return request_xg.post('merchant/admin.KnowledgeBase/searchTest', data);
};
//修改用户状态
export const changeStatusApi = (data) => {
  return request.post('user/admin.user/changeStatus', data);
};

//会员卡列表
export const vipCardList = (data) => {
  return request.post('merchant/admin.MemberCard/index', data);
};

//创建会员卡
export const createCipCard = (data) => {
  return request.post('merchant/admin.MemberCard/create', data);
};

//删除会员卡
export const deleteCipCard = (data) => {
  return request.post('merchant/admin.MemberCard/delete', data);
};

//修改会员卡
export const updateCipCard = (data) => {
  return request.post('merchant/admin.MemberCard/update', data);
};

//用户大会员列表
export const getUserMembers = (data) => {
  return request.post('user/admin.member/userMembers', data);
};

//大会员卡购买订单列表
export const getCardOrderList = (data) => {
  return request.post('user/admin.member/cardOrderList', data);
};

//大会员申请列表
export const getBigVipApplyList = (data) => {
  return request.post('user/admin.member/bigVipApplyList', data);
};

//审批大会员申请
export const approveBigVipApply = (data) => {
  return request.post('user/admin.member/approveBigVipApply', data);
};

//查询会员卡订单支付结果
export const cardOrderQueryBuy = (data) => {
  return request.post('user/admin.member/cardOrderQueryBuy', data);
};

//会员提现记录
export const userWithdrawRecords = (data) => {
  return request.post('user/admin.member/userWithdrawRecords', data);
};

//会员提现审核
export const approveWithdraw = (data) => {
  return request.post('user/admin.member/approveWithdraw', data);
};

//AI主页列表
export const expoListApi = (data) => {
  return request.post('zhanhui/admin.index/lists', data);
};

//创建AI主页
export const createExpoApi = (data) => {
  return request.post('zhanhui/admin.index/create', data);
};

//修改AI主页
export const updateExpoApi = (data) => {
  return request.post('zhanhui/admin.index/update', data);
};

//删除AI主页
export const deleteExpoApi = (data) => {
  return request.post('zhanhui/admin.index/delete', data);
};

//获取AI主页详情
export const getExpoDetailApi = (data) => {
  return request.post('zhanhui/admin.index/detail', data);
};

//改变AI主页状态
export const changeExpoStatusApi = (data) => {
  return request.post('zhanhui/admin.index/changeStatus', data);
};

//AI主页场景问题列表
export const expoFaqListsApi = (data) => {
  return request.post('zhanhui/admin.index/faqLists', data);
};

//AI主页场景问题创建
export const faqCreateApi = (data) => {
  return request.post('zhanhui/admin.index/faqCreate', data);
};

//AI主页场景问题修改
export const faqUpdateApi = (data) => {
  return request.post('zhanhui/admin.index/faqUpdate', data);
};

//AI主页场景问题详情
export const faqDetailApi = (data) => {
  return request.post('zhanhui/admin.index/faqDetail', data);
};

//AI主页场景问题删除
export const faqDeleteApi = (data) => {
  return request.post('zhanhui/admin.index/faqDelete', data);
};

//AI主页banner图创建
export const bannerCreateApi = (data) => {
  return request.post('zhanhui/admin.index/bannerCreate', data);
};
//AI主页banner图列表
export const bannerListsApi = (data) => {
  return request.post('zhanhui/admin.index/bannerLists', data);
};

//AI主页banner图删除
export const bannerDeleteApi = (data) => {
  return request.post('zhanhui/admin.index/bannerDelete', data);
};

//AI主页banner图修改
export const bannerUpdateApi = (data) => {
  return request.post('zhanhui/admin.index/bannerUpdate', data);
};

//AI主页banner图自定义标题修改
export const bannerTitleUpdateApi = (data) => {
  return request.post('zhanhui/admin.index/bannerTitleUpdate', data);
};

//AI主页充值AI点数
export const rechargePointApi = (data) => {
  return request.post('zhanhui/admin.index/rechargePoint', data);
};

//创建AI主页兑换码
export const exchangeCodeCreateApi = (data) => {
  return request.post('zhanhui/admin.index/exchangeCodeCreate', data);
};

//AI主页兑换码列表
export const exchangeCodeListsApi = (data) => {
  return request.post('zhanhui/admin.index/exchangeCodeLists', data);
};

//删除兑换码
export const exchangeCodeDeleteApi = (data) => {
  return request.post('zhanhui/admin.index/exchangeCodeDelete', data);
};
//智能体厂商列表
export const getAiAgentVendorListApi = () => {
  return request.post('square/admin.chatGoods/aiAgentVendorList');
};

//创建AI智能体
export const merchantAiAgentCreateApi = (data) => {
  return request.post('square/admin.chatGoods/merchantAiAgentCreate', data);
};

//修改AI智能体
export const merchantAiAgentUpdateApi = (data) => {
  return request.post('square/admin.chatGoods/merchantAiAgentUpdate', data);
};

//AI智能体列表
export const merchantAgentListApi = (data) => {
  return request.post('square/admin.chatGoods/merchantAgentList', data);
};

//展会状态更改
export const adminChangeStatusApi = (data) => {
  return request.post('zhanhui/admin.index/zhanhuiAdminUserUpdate', data);
}

//权限菜单列表
export const permissionMenuListApi = (data) => {
  return request.post('admin/admin.Permission/list', data);
};
//角色创建
export const createRoleApi = (data) => {
  return request.post('admin/admin.MerchantRole/create', data);
};
//角色编辑
export const editRoleApi = (data) => {
  return request.post('admin/admin.MerchantRole/edit', data);
};

//角色删除
export const deleteRoleApi = (data) => {
  return request.post('admin/admin.MerchantRole/delete', data);
};

//角色详情
export const getRoleDetailApi = (data) => {
  return request.post('admin/admin.MerchantRole/get', data);
};

//角色列表
export const getRoleListApi = (data) => {
  return request.post('admin/admin.MerchantRole/list', data);
};
//绑定商户管理员角色
export const bindMerchantRoleApi = (data) => {
  return request.post('admin/admin.AdminUserRole/bindMerchantRole', data);
};
//获取商户管理员角色
export const getMerchantRolesApi = (data) => {
  return request.post('admin/admin.AdminUserRole/getMerchantRoles', data);
};
//获取商户管理员角色菜单
export const getMerchantRolesMenusApi = () => {
  return request.post('admin/admin.MerchantRole/menus');
};

//获取智能体分类列表
export const getAiAgentCategoryApi = (data) => {
  return request.post('useragent/admin.AiAgentCategory/index', data);
};
//创建智能体分类列表
export const createAiAgentCategoryApi = (data) => {
  return request.post('useragent/admin.AiAgentCategory/create', data);
};

//修改智能体分类列表
export const updateAiAgentCategoryApi = (data) => {
  return request.post('useragent/admin.AiAgentCategory/update', data);
};

//删除智能体分类列表
export const deleteAiAgentCategoryApi = (data) => {
  return request.post('useragent/admin.AiAgentCategory/delete', data);
};

//获取智能体列表
export const getAiAgentListApi = (data) => {
  return request.post('useragent/admin.AiAgent/index', data);
};

//获取智能体详情
export const getAiAgentDetailApi = (data) => {
  return request.post('useragent/admin.AiAgent/detail', data);
};

//审核用户智能体
export const auditAiAgentApi = (data) => {
  return request.post('useragent/admin.AiAgent/audit', data);
};

//修改用户智能体状态
export const updateAiAgentStatusApi = (data) => {
  return request.post('useragent/admin.AiAgent/updateStatus', data);
};

//修改用户智能体详情
export const updateAiAgentInfoApi = (data) => {
  return request.post('useragent/admin.AiAgent/update', data);
};

//平台配置列表
export const getAiAgentConfigApi = (data) => {
  return request.post('useragent/admin.AiAgentConfig/index', data);
};

//修改平台配置列表
export const updateAiAgentConfigApi = (data) => {
  return request.post('useragent/admin.AiAgentConfig/update', data);
};

//展会管理员列表
export const getZhanhuiAdminUsersApi = (data) => {
  return request.post('zhanhui/admin.index/zhanhuiAdminUsers', data);
};

//展会管理员创建
export const zhanhuiAdminUserCreateApi = (data) => {
  return request.post('zhanhui/admin.index/zhanhuiAdminUserCreate', data);
};

//展会管理员删除
export const zhanhuiAdminUserDeleteApi = (data) => {
  return request.post('zhanhui/admin.index/zhanhuiAdminUserDelete', data);
};

//付费智能体会员卡订单列表
export const getAiAgentMembershipOrderApi = (data) => {
  return request.post('useragent/admin.AiAgentMembershipOrder/index', data);
};
//添加付费智能体会员
export const manualAddOrderApi = (data) => {
  return request.post('useragent/admin.AiAgentMembershipOrder/manualAdd', data);
};

//获取AI智能体普通套餐列表
export const getPayAgentPackageListApi = (data) => {
  return request.post('useragent/admin.AiAgentMembershipPackage/index', data);
};
//新增AI智能体普通套餐
export const createPayAgentPackageApi = (data) => {
  return request.post('useragent/admin.AiAgentMembershipPackage/create', data);
};
//修改AI智能体普通套餐
export const updatePayAgentPackageApi = (data) => {
  return request.post('useragent/admin.AiAgentMembershipPackage/update', data);
};
//修改AI智能体普通套餐
export const deletePayAgentPackageApi = (data) => {
  return request.post('useragent/admin.AiAgentMembershipPackage/delete', data);
};
//更新套餐状态
export const updateStatusPayAgentPackagepi = (data) => {
  return request.post('useragent/admin.AiAgentMembershipPackage/updateStatus', data);
};
//精品智能体订单列表
export const getPayAgentpBoutinqueOrderApi = (data) => {
  return request.post('useragent/admin.AiAgentOrder/index', data);
};

//添加精品付费智能体会员
export const manualAddBoutiqueOrderApi = (data) => {
  return request.post('useragent/admin.AiAgentOrder/manualAdd', data);
};
