<template>
  <div>
    <el-container class="wrapper">
      <el-main v-loading="loading">
        <div class="header-box">
          <el-form :inline="true" :model="reqForm" class="demo-form-inline">
            <el-form-item label="订单编号">
              <el-input v-model="reqForm.orderNo" placeholder="请输入订单编号" clearable />
            </el-form-item>
            <el-form-item label="用户手机号">
              <el-input v-model="reqForm.userMobile" placeholder="请输入用户手机号" clearable />
            </el-form-item>
            <el-form-item label="订单状态">
              <div class="mode-box">
                <div :class="['item', { active: modeStatus === item.value }]" v-for="(item, index) in orderStatusList"
                  :key="index" @click="onChangeOrderStatus(item.value)">
                  {{ item.name }}
                </div>
              </div>
            </el-form-item>
            <el-form-item label="套餐类型">
              <el-select v-model="reqForm.packageType" placeholder="请选择套餐类型" clearable>
                <el-option label="全部" value="" />
                <el-option label="月卡" :value="1" />
                <el-option label="季卡" :value="2" />
                <el-option label="年卡" :value="3" />
                <el-option label="自定义" :value="4" />
              </el-select>
            </el-form-item>
            <el-form-item label="时间范围">
              <el-date-picker v-model="dateRange" type="datetimerange" range-separator="至" start-placeholder="开始时间"
                end-placeholder="结束时间" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss"
                @change="onDateRangeChange" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="onSearch">搜索</el-button>
              <el-button @click="onReset">重置</el-button>
              <el-button type="success" @click="openAddDialog">新增会员</el-button>
            </el-form-item>
          </el-form>
        </div>

        <el-table :data="orderList" border style="width: 100%">
          <el-table-column prop="sysId" label="订单ID" width="80" />
          <el-table-column label="购买用户" width="200">
            <template #default="scope">
              <div class="user-info">
                <el-avatar :src="scope.row.buyer.headImgurl" :size="30" />
                <div class="user-details">
                  <div>{{ scope.row.buyer.nickname }}</div>
                  <div class="mobile">{{ scope.row.buyer.mobile }}</div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="orderNo" label="订单编号" width="180" />
          <el-table-column label="套餐信息" width="200">
            <template #default="scope">
              <div class="package-info">
                <div class="package-name">{{ scope.row.package.packageName }}</div>
                <!-- <div class="package-type">{{ scope.row.packageTypeText }}</div> -->
                <div class="duration">{{ scope.row.durationDays }}天</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="金额信息" width="150">
            <template #default="scope">
              <div class="amount-info">
                <div class="pay-amount">支付: ¥{{ scope.row.payAmountYuan }}</div>
                <!-- <div class="original-amount">原价: ¥{{ scope.row.originalAmountYuan }}</div> -->
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="orderStatusText" label="订单状态" width="100">
            <template #default="scope">
              <el-tag :type="getStatusTagType(scope.row.orderStatus)" size="small">
                {{ scope.row.orderStatusText }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="邀请人" width="150">
            <template #default="scope">
              <div v-if="scope.row.inviter" class="inviter-info">
                <el-avatar :src="scope.row.inviter.headImgurl" :size="24" />
                <span class="inviter-name">{{ scope.row.inviter.nickname }}</span>
              </div>
              <span v-else class="no-inviter">无</span>
            </template>
          </el-table-column>
          <el-table-column label="佣金信息" width="120">
            <template #default="scope">
              <div class="commission-info">
                <div>平台费: ¥{{ scope.row.platformFeeYuan }}</div>
                <div v-if="scope.row.inviterCommissionYuan">
                  邀请佣金: ¥{{ scope.row.inviterCommissionYuan }}
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="createTimeText" label="创建时间" width="160" />
          <el-table-column prop="payTimeText" label="支付时间" width="160" />
          <el-table-column prop="expireTimeText" label="到期时间" width="160" />
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="scope">
              <el-button size="small" type="primary" @click="viewDetail(scope.row)">
                查看详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-main>

      <el-footer>
        <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="total"
          :current-page="reqForm.page" :page-size="reqForm.limit" :page-sizes="[10, 20, 50, 100]"
          @current-change="handlePageChange" @size-change="handleSizeChange" />
      </el-footer>
    </el-container>

    <!-- 详情弹窗 -->
    <el-dialog v-model="detailDialogVisible" title="订单详情" width="800px" :before-close="handleDetailClose">
      <div v-if="currentOrder" class="order-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="订单ID">{{ currentOrder.sysId }}</el-descriptions-item>
          <el-descriptions-item label="订单编号">{{ currentOrder.orderNo }}</el-descriptions-item>
          <el-descriptions-item label="交易流水号">{{ currentOrder.transactionNo || '无' }}</el-descriptions-item>
          <el-descriptions-item label="订单状态">
            <el-tag :type="getStatusTagType(currentOrder.orderStatus)">
              {{ currentOrder.orderStatusText }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="购买用户">{{ currentOrder.buyerNickname }}</el-descriptions-item>
          <el-descriptions-item label="用户手机">{{ currentOrder.buyerMobile }}</el-descriptions-item>
          <el-descriptions-item label="套餐名称">{{ currentOrder.packageName }}</el-descriptions-item>
          <el-descriptions-item label="套餐类型">{{ currentOrder.packageTypeText }}</el-descriptions-item>
          <el-descriptions-item label="套餐时长">{{ currentOrder.durationDays }}天</el-descriptions-item>
          <el-descriptions-item label="原价">¥{{ currentOrder.originalAmountYuan }}</el-descriptions-item>
          <el-descriptions-item label="支付金额">¥{{ currentOrder.payAmountYuan }}</el-descriptions-item>
          <el-descriptions-item label="平台费用">¥{{ currentOrder.platformFeeYuan }}</el-descriptions-item>
          <el-descriptions-item label="邀请人">{{ currentOrder.inviterNickname || '无' }}</el-descriptions-item>
          <el-descriptions-item label="邀请佣金">¥{{ currentOrder.inviterCommissionYuan || '0' }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ currentOrder.createTimeText }}</el-descriptions-item>
          <el-descriptions-item label="支付时间">{{ currentOrder.payTimeText || '未支付' }}</el-descriptions-item>
          <el-descriptions-item label="到期时间">{{ currentOrder.expireTimeText }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 新增会员弹窗 -->
    <el-dialog v-model="addDialogVisible" title="新增会员" width="600px" :before-close="handleAddDialogClose">
      <el-form :model="addForm" :rules="addFormRules" ref="addFormRef" label-width="120px">
        <el-form-item label="套餐选择" prop="packageGuid" required>
          <el-select v-model="addForm.packageGuid" placeholder="请选择套餐" style="width: 100%" @change="onPackageChange">
            <el-option v-for="pkg in packageList" :key="pkg.guid"
              :label="`${pkg.packageName} - ¥${pkg.salePrice / 100}`" :value="pkg.guid">
              <div class="package-option">
                <span class="package-name">{{ pkg.packageName }}</span>
                <span class="package-price">¥{{ pkg.salePrice / 100 }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="用户选择" required>
          <el-radio-group v-model="userSelectType" @change="onUserSelectTypeChange">
            <el-radio label="userGuid">用户GUID</el-radio>
            <el-radio label="nickname">用户昵称</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item v-if="userSelectType === 'userGuid'" label="用户GUID" prop="userGuid">
          <el-input v-model="addForm.userGuid" placeholder="请输入用户GUID" clearable />
        </el-form-item>

        <el-form-item v-if="userSelectType === 'nickname'" label="用户昵称" prop="nickname">
          <el-input v-model="addForm.nickname" placeholder="请输入用户昵称" clearable />
        </el-form-item>

        <el-form-item v-if="selectedPackage" label="套餐信息">
          <div class="package-preview">
            <div class="package-detail">
              <div><strong>套餐名称：</strong>{{ selectedPackage.packageName }}</div>
              <div><strong>套餐类型：</strong>{{ getPackageTypeText(selectedPackage.packageType) }}</div>
              <div><strong>套餐价格：</strong>¥{{ selectedPackage.salePrice / 100 }}</div>
              <div><strong>套餐时长：</strong>{{ selectedPackage.durationDays }}天</div>
            </div>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleAddDialogClose">取消</el-button>
          <el-button type="primary" @click="handleAddMember" :loading="addLoading">确认添加</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { getAiAgentMembershipOrderApi, manualAddOrderApi, getAiAgentPackageListApi } from '@/api';
import { ElMessage, type FormInstance, type FormRules } from 'element-plus';

// 响应式数据
const loading = ref(false);
const total = ref(0);
const modeStatus = ref('');
const dateRange = ref<[string, string] | null>(null);
const detailDialogVisible = ref(false);
const currentOrder = ref<any>(null);

// 新增会员相关数据
const addDialogVisible = ref(false);
const addLoading = ref(false);
const addFormRef = ref<FormInstance>();
const userSelectType = ref('userGuid');
const packageList = ref<any[]>([]);
const selectedPackage = ref<any>(null);

// 新增会员表单数据
const addForm = reactive({
  merchantGuid: '', // 商户uuid，后端会自动填充
  packageGuid: '', // 套餐guid
  userGuid: '', // 用户guid
  nickname: '' // 用户昵称
});

// 表单验证规则
const addFormRules: FormRules = {
  packageGuid: [
    { required: true, message: '请选择套餐', trigger: 'change' }
  ],
  userGuid: [
    {
      required: true,
      message: '请输入用户GUID',
      trigger: 'blur',
      validator: (rule, value, callback) => {
        if (userSelectType.value === 'userGuid' && !value) {
          callback(new Error('请输入用户GUID'));
        } else {
          callback();
        }
      }
    }
  ],
  nickname: [
    {
      required: true,
      message: '请输入用户昵称',
      trigger: 'blur',
      validator: (rule, value, callback) => {
        if (userSelectType.value === 'nickname' && !value) {
          callback(new Error('请输入用户昵称'));
        } else {
          callback();
        }
      }
    }
  ]
};

// 订单状态列表
const orderStatusList = reactive([
  { name: '全部', value: '' },
  { name: '待支付', value: 100 },
  { name: '已支付', value: 200 },
  { name: '取消支付', value: 300 },
  { name: '支付超时', value: 400 },
  { name: '已退款', value: 500 },
]);

// 请求参数
const reqForm = reactive({
  merchantGuid: '', // 商户uuid，后端会自动填充
  orderStatus: '' as string | number, // 订单状态
  packageType: '' as string | number, // 套餐类型
  orderNo: '', // 订单编号
  userMobile: '', // 用户手机号
  startTime: '', // 开始时间
  endTime: '', // 结束时间
  page: 1, // 页数
  limit: 20 // 每页条数
});

// 订单列表
const orderList = ref<any[]>([]);

// 获取订单状态标签类型
const getStatusTagType = (status: number) => {
  switch (status) {
    case 100: return 'warning'; // 待支付
    case 200: return 'success'; // 已支付
    case 300: return 'info'; // 取消支付
    case 400: return 'danger'; // 支付超时
    case 500: return 'primary'; // 已退款
    default: return 'info';
  }
};

// 切换订单状态
const onChangeOrderStatus = (value: string | number) => {
  modeStatus.value = value.toString();
  reqForm.orderStatus = value;
  reqForm.page = 1;
  getList();
};

// 时间范围变化
const onDateRangeChange = (dates: [string, string] | null) => {
  if (dates) {
    reqForm.startTime = dates[0];
    reqForm.endTime = dates[1];
  } else {
    reqForm.startTime = '';
    reqForm.endTime = '';
  }
};

// 获取订单列表
const getList = async () => {
  try {
    loading.value = true;
    const response = await getAiAgentMembershipOrderApi(reqForm);

    if (response.data) {
      total.value = response.data.total || 0;
      orderList.value = response.data.data || [];
    }
  } catch (error) {
    console.error('获取订单列表失败:', error);
    ElMessage.error('获取订单列表失败');
    orderList.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 搜索
const onSearch = () => {
  reqForm.page = 1;
  getList();
};

// 重置
const onReset = () => {
  Object.assign(reqForm, {
    orderStatus: '',
    packageType: '',
    orderNo: '',
    userMobile: '',
    startTime: '',
    endTime: '',
    page: 1,
    limit: 20
  });
  modeStatus.value = '';
  dateRange.value = null;
  getList();
};

// 分页变化
const handlePageChange = (page: number) => {
  reqForm.page = page;
  getList();
};

// 每页条数变化
const handleSizeChange = (size: number) => {
  reqForm.limit = size;
  reqForm.page = 1;
  getList();
};

// 查看详情
const viewDetail = (order: any) => {
  currentOrder.value = order;
  detailDialogVisible.value = true;
};

// 关闭详情弹窗
const handleDetailClose = () => {
  detailDialogVisible.value = false;
  currentOrder.value = null;
};

// 获取套餐类型文本
const getPackageTypeText = (type: number) => {
  switch (type) {
    case 1: return '月卡';
    case 2: return '季卡';
    case 3: return '年卡';
    case 4: return '自定义';
    default: return '未知';
  }
};

// 获取套餐列表
const getPackageList = async () => {
  try {
    const response = await getAiAgentPackageListApi({});
    if (response.data) {
      packageList.value = response.data.data || response.data || [];
    }
  } catch (error) {
    console.error('获取套餐列表失败:', error);
    ElMessage.error('获取套餐列表失败');
    packageList.value = [];
  }
};

// 打开新增会员弹窗
const openAddDialog = () => {
  addDialogVisible.value = true;
  getPackageList();
  resetAddForm();
};

// 重置新增表单
const resetAddForm = () => {
  Object.assign(addForm, {
    merchantGuid: '',
    packageGuid: '',
    userGuid: '',
    nickname: ''
  });
  userSelectType.value = 'userGuid';
  selectedPackage.value = null;
  addFormRef.value?.clearValidate();
};

// 套餐选择变化
const onPackageChange = (packageGuid: string) => {
  selectedPackage.value = packageList.value.find(pkg => pkg.guid === packageGuid) || null;
};

// 用户选择类型变化
const onUserSelectTypeChange = (type: string) => {
  // 清空另一个字段的值
  if (type === 'userGuid') {
    addForm.nickname = '';
  } else {
    addForm.userGuid = '';
  }
  addFormRef.value?.clearValidate();
};

// 关闭新增弹窗
const handleAddDialogClose = () => {
  addDialogVisible.value = false;
  resetAddForm();
};

// 添加会员
const handleAddMember = async () => {
  if (!addFormRef.value) return;

  try {
    await addFormRef.value.validate();

    addLoading.value = true;

    // 构建请求参数
    const params: any = {
      packageGuid: addForm.packageGuid,
    };

    // 根据选择类型添加用户信息
    if (userSelectType.value === 'userGuid') {
      params.userGuid = addForm.userGuid;
    } else {
      params.nickname = addForm.nickname;
    }

    const { data } = await manualAddOrderApi(params);
    if (data.data) {
      handleAddDialogClose();
      getList(); // 刷新列表
    } else {
      ElMessage.warning(data.message);
    }

  } catch (error: any) {
    console.error('添加会员失败:', error);

  } finally {
    addLoading.value = false;
  }
};

// 初始化加载数据
onMounted(() => {
  getList();
});
</script>

<style scoped lang="scss">
.wrapper {
  height: 100%;

  .header-box {
    background: #f5f7fa;
    padding: 20px;
    margin-bottom: 20px;
    border-radius: 4px;
    border: 1px solid #e4e7ed;
  }
}

.mode-box {
  background-color: #eee;
  display: flex;
  padding: 4px;
  border-radius: 6px;
  width: 400px;

  .item {
    background-color: #fff;
    margin: 0 2px;
    flex: 1;
    border-radius: 4px;
    text-align: center;
    cursor: pointer;
    height: 28px;
    line-height: 28px;
    font-size: 12px;
    transition: all 0.3s;

    &:hover {
      background-color: #f0f9ff;
    }

    &.active {
      background-color: #409eff;
      color: #fff;
    }
  }
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;

  .user-details {
    .mobile {
      font-size: 12px;
      color: #666;
    }
  }
}

.package-info {
  .package-name {
    font-weight: 500;
    margin-bottom: 4px;
  }

  .package-type,
  .duration {
    font-size: 12px;
    color: #666;
  }
}

.amount-info {
  .pay-amount {
    color: #f56c6c;
    font-weight: 500;
    margin-bottom: 4px;
  }

  .original-amount {
    font-size: 12px;
    color: #999;
    text-decoration: line-through;
  }
}

.inviter-info {
  display: flex;
  align-items: center;
  gap: 6px;

  .inviter-name {
    font-size: 12px;
  }
}

.no-inviter {
  color: #999;
  font-size: 12px;
}

.commission-info {
  font-size: 12px;

  div {
    margin-bottom: 2px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.order-detail {
  .el-descriptions {
    margin-top: 20px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

// 新增会员弹窗样式
.package-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .package-name {
    font-weight: 500;
  }

  .package-price {
    color: #f56c6c;
    font-weight: 600;
  }
}

.package-preview {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 16px;

  .package-detail {
    div {
      margin-bottom: 8px;
      line-height: 1.5;

      &:last-child {
        margin-bottom: 0;
      }

      strong {
        color: #333;
        margin-right: 8px;
      }
    }
  }
}
</style>