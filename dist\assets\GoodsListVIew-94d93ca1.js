import{d as L,r as i,a as M,c as F,b as e,w as t,Q as P,R as q,S as G,e as R,o as f,T as z,h as a,I as h,f as O,i as u,U as V,F as Q,V as W,W as X,k as Z,l as $,m as j,n as H,X as J,p as K,q as ee,s as te,v as ae,y as oe,x as le,Y as ne,Z as re}from"./index-8fc71dda.js";/* empty css                   *//* empty css                      *//* empty css                 *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                     *//* empty css                       *//* empty css                       *//* empty css                   */const se={class:"hearder-box"},Ve=L({__name:"GoodsListVIew",setup(de){const b=i(0),m=i(!1),g=i(0),o=M({orderNo:"",orderStatus:"",merchantGuid:"",orderTime:"",parentUid:"",pageSize:10,page:1});let y=i([]),w=i([]);const c=async()=>{m.value=!0;let r=await P(o),l=await q(o);g.value=l.data,m.value=!1,b.value=r.data.total,y.value=r.data.data},C=async()=>{let r=await G();w.value=r.data},T=async r=>{o.page=r,c()},E=async r=>{let p=(await W({orderNo:r.orderNo})).data.isPay?"已成功支付":"未支付";X.alert(p,"查询结果",{confirmButtonText:"确定"})},k=()=>{c()};return c(),C(),(r,l)=>{const p=Z,d=$,_=j,x=H,S=J,v=K,N=ee,s=te,U=ae,Y=oe,I=le,B=ne,D=R,A=re;return f(),F("div",null,[e(D,{class:"wrapper"},{default:t(()=>[z((f(),h(Y,null,{default:t(()=>[O("div",se,[e(N,{inline:!0,model:a(o),class:"demo-form-inline"},{default:t(()=>[e(d,{label:"订单编号"},{default:t(()=>[e(p,{modelValue:a(o).orderNo,"onUpdate:modelValue":l[0]||(l[0]=n=>a(o).orderNo=n),placeholder:"订单编号"},null,8,["modelValue"])]),_:1}),e(d,{label:"上级用户Id"},{default:t(()=>[e(p,{modelValue:a(o).parentUid,"onUpdate:modelValue":l[1]||(l[1]=n=>a(o).parentUid=n),placeholder:"上级用户Id"},null,8,["modelValue"])]),_:1}),e(d,{label:"支付状态"},{default:t(()=>[e(x,{modelValue:a(o).orderStatus,"onUpdate:modelValue":l[2]||(l[2]=n=>a(o).orderStatus=n),placeholder:"请选择"},{default:t(()=>[e(_,{label:"全部",value:""}),e(_,{label:"待支付",value:"100"}),e(_,{label:"已支付",value:"200"})]),_:1},8,["modelValue"])]),_:1}),e(d,{label:"订单日期"},{default:t(()=>[e(S,{modelValue:a(o).orderTime,"onUpdate:modelValue":l[3]||(l[3]=n=>a(o).orderTime=n),type:"daterange",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD","start-placeholder":"开始日期","end-placeholder":"结束日期"},null,8,["modelValue"])]),_:1}),e(d,{label:"订单总金额"},{default:t(()=>[u(V(a(g)),1)]),_:1}),e(d,null,{default:t(()=>[e(v,{type:"primary",onClick:k},{default:t(()=>[u("搜索")]),_:1})]),_:1})]),_:1},8,["model"])]),e(U,{data:a(y),border:"",style:{width:"100%"}},{default:t(()=>[e(s,{prop:"platformUserSysId",label:"订单Id",width:"80"}),e(s,{prop:"orderNo",label:"订单编号"}),e(s,{prop:"chatCount",label:"聊天点数",width:"120"}),e(s,{prop:"amount",label:"订单金额",width:"120"}),e(s,{prop:"nickname",label:"支付用户"}),e(s,{prop:"orderStatusText",label:"支付状态"},{default:t(n=>[u(V(n.row.orderStatusText)+" ",1),n.row.orderStatusText==="待支付"?(f(),h(v,{key:0,size:"small",type:"primary",onClick:ie=>E(n.row)},{default:t(()=>[u("查询")]),_:2},1032,["onClick"])):Q("",!0)]),_:1}),e(s,{prop:"createTime",label:"下单时间"}),e(s,{prop:"modifyTime",label:"付款时间"})]),_:1},8,["data"])]),_:1})),[[A,a(m)]]),e(B,null,{default:t(()=>[e(I,{background:"",layout:"prev,pager, next",total:a(b),"current-page":a(o).page,onCurrentChange:T},null,8,["total","current-page"])]),_:1})]),_:1})])}}});export{Ve as default};
